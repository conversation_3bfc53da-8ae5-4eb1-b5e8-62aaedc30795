import { Item } from "@/lib/common/inventory-storage";
import { WebCallManager } from "@/lib/common/webcall";

export function isIronman(): boolean {
  return userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1;
}

export function getMaxHunger(): number {
  return Math.floor(50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5));
}

export function getCash(): number {
  return parseInt(userVars["DFSTATS_df_cash"]);
}

export async function useItem(item: Item) {
  let action;
  if (item.data["healthrestore"] > 0) {
    action = "newuse";
  } else if (item.data["armourrestore"] > 0) {
    action = "userepairkit";
  } else if (item.data["foodrestore"] > 0) {
    action = "newconsume";
  } else if (
    item.data["boostdamagehours"] > 0 ||
    item.data["boostexphours"] > 0 ||
    item.data["boostspeedhours"] > 0 ||
    item.data["boostdamagehours_ex"] > 0 ||
    item.data["boostexphours_ex"] > 0 ||
    item.data["boostspeedhours_ex"] > 0
  ) {
    action = "newboost";
  } else if (
    item.data["opencontents"] &&
    item.data["opencontents"].length > 0
  ) {
    action = "newopen";
  } else if (item.data["moneygift"] === true) {
    action = "moneygift";
  } else if (item.data["gm_days"] !== "0") {
    action = "newuse";
  } else {
    throw new Error(`useItem: unsupported item type ${item.type}`);
  }

  const dataArr = {};
  dataArr["creditsnum"] = 0;
  dataArr["buynum"] = 0;
  dataArr["renameto"] = "undefined`undefined";
  dataArr["expected_itemprice"] = "-1";
  dataArr["expected_itemtype2"] = "";
  dataArr["expected_itemtype"] = item.type;
  dataArr["itemnum2"] = "0";
  dataArr["itemnum"] = item.slot;
  dataArr["price"] = 0;
  dataArr["action"] = action;

  const result = await WebCallManager.runWebCall("inventory_new", dataArr);
  updateIntoArr(flshToArr(result, "DFSTATS_") as any, userVars);
  populateInventory();
  populateCharacterInventory();
  updateAllFields();
}
