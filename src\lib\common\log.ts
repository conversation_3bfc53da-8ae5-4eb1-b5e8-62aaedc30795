export class Log {
  static format(...args: any[]) {
    return ["ZYScript:", ...args];
  }
  static error(...args: any[]) {
    console.error(...Log.format(...args));
  }
  static warn(...args: any[]) {
    console.warn(...Log.format(...args));
  }
  static info(...args: any[]) {
    console.info(...Log.format(...args));
  }
  static debug(...args: any[]) {
    console.debug(...Log.format(...args));
  }
}
