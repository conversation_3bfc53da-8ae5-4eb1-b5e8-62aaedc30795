import { getCash } from "@/lib/common/character";

export function h<T extends keyof HTMLElementTagNameMap>(
  tag: T,
  props: Partial<HTMLElementTagNameMap[T]> | null,
  ...children
): Element {
  const el = document.createElement(tag);

  for (const [key, value] of Object.entries(props || {})) {
    if (key === "style" && value && typeof value === "object") {
      Object.assign(el.style, value);
    } else if (key.startsWith("on") && typeof value === "function") {
      el.addEventListener(key.substring(2).toLowerCase(), value);
    } else {
      el.setAttribute(key, value);
    }
  }

  for (const child of children.flat()) {
    el.appendChild(
      child instanceof Node ? child : document.createTextNode(child)
    );
  }

  return el;
}

export function makeColoredPriceBasedOnCash(
  price: number,
  cash?: number
): HTMLSpanElement {
  const cashToUse = cash ?? getCash();
  return (
    <span style={{ color: cashToUse < price ? "red" : "white" }}>
      ${nf.format(price)}
    </span>
  );
}
