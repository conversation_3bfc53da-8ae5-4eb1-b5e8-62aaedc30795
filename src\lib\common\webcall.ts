import { Log } from "@/lib/common/log";

/**
 * @param name name of window property to override
 * @param f function that produces the new value of the property based on the old value
 */
export function overrideGlobal(name: string, f: (oldValue: any) => any) {
  Object.defineProperty(unsafeWindow, name, {
    value: f((unsafeWindow as any)[name]),
    writable: false,
    configurable: false,
  });
}

export function asyncWebCall(
  call: string,
  params: Record<string, any>,
  hashed: boolean = true
): Promise<string> {
  params["userID"] = userVars["userID"];
  params["password"] = userVars["password"];
  params["pagetime"] = userVars["pagetime"];
  params["templateID"] = userVars["template_ID"];
  params["sc"] = userVars["sc"];
  params["gv"] = 21;

  return new Promise((resolve, reject) => {
    try {
      webCall(
        call,
        params,
        (data) => {
          resolve(data);
        },
        hashed
      );
    } catch (e) {
      reject(e);
    }
  });
}

export interface WebCallTask {
  taskFn: () => Promise<any>;
  promise: Promise<any>;
  status: "pending" | "running" | "done";
}

export class WebCallManager {
  static MAX_CONCURRENT_CALLS = 1;
  static RUNNING_TASKS: WebCallTask[] = [];
  static TASK_QUEUE: WebCallTask[] = [];

  /**
   * should be called when a task ends, or when a task is queued
   */
  private static tick() {
    if (this.RUNNING_TASKS.length >= this.MAX_CONCURRENT_CALLS) {
      return;
    }

    const task = this.TASK_QUEUE.shift();
    if (!task) {
      return;
    }

    task.status = "running";
    this.RUNNING_TASKS.push(task);
    task.promise = task.taskFn();
    task.promise.finally(() => {
      task.status = "done";
      this.RUNNING_TASKS = this.RUNNING_TASKS.filter((t) => t !== task);
      this.tick();
    });
  }

  private static queueTask(taskFn: () => Promise<any>): WebCallTask {
    const task: WebCallTask = {
      taskFn,
      promise: null,
      status: "pending",
    };

    this.TASK_QUEUE.push(task);
    this.tick();
    return task;
  }

  static spawnWebCall(
    call: string,
    params: Record<string, any>,
    hashed: boolean = true
  ): WebCallTask {
    return this.queueTask(() => asyncWebCall(call, params, hashed));
  }

  static async runWebCall(
    call: string,
    params: Record<string, any>,
    hashed: boolean = true
  ): Promise<any> {
    return await this.spawnWebCall(call, params, hashed).promise;
  }
}
