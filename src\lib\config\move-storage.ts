import { Inventory, Storage } from "@/lib/common/inventory-storage";
import { Item } from "@/lib/common/inventory-storage";
import { Log } from "@/lib/common/log";

export function uiAddMoveStorageButtons() {
  // copied directly from storage page so no need for jsx here
  const html = `<div style="position: absolute; right: 13px; bottom: 86px; z-index: 1;"><button id="storagetoinv" data-pmoverride=""><img src="/onlinezombiemmo/hotrods/hotrods_v${hrV}/HTML5/images/movein.png" width="40" data-amchild=""></button><button id="invtostorage" data-pmoverride=""><img src="/onlinezombiemmo/hotrods/hotrods_v${hrV}/HTML5/images/moveout.png" width="40" data-amchild=""></button></div>`;

  if (inventoryHolder) {
    document
      .getElementById("inventoryholder")
      .insertAdjacentHTML("beforeend", html);

    document.getElementById("storagetoinv").addEventListener("click", () => {
      Storage.takeAllFromStorage();
    });

    document.getElementById("invtostorage").addEventListener("click", () => {
      Inventory.moveAllToStorage();
    });
  }

  // move up discard button
  const discardButton = document.querySelector<HTMLElement>(
    'div.fakeSlot.hoverEffect[data-action="discard"]'
  );
  if (discardButton) {
    discardButton.style.bottom = "150px";
    const discardLabel = [
      ...document.querySelectorAll<HTMLElement>("div.opElem"),
    ].find((el) => el.textContent.trim() === "Discard");
    discardLabel.style.bottom = "134px";
  }
}

function mouseMoveHandler(e: MouseEvent) {
  if (!(e.target instanceof HTMLElement)) {
    return;
  }

  if (infoBox.style.visibility !== "visible") {
    return;
  }

  const item = Item.fromElement(e.target as HTMLElement);
  if (item.fakeItem) {
    return;
  }

  console.log(item);
}

export function addMoveStorageShortcut() {
  if (!unsafeWindow.storageBox) {
    Log.error("storageBox not found");
    return;
  }

  inventoryHolder.addEventListener("mousemove", mouseMoveHandler);
}
