export function removeOutpostButtonDelay() {
  const outpostLinkButtons = [];
  for (const elem of document.getElementsByClassName("opElem")) {
    if (elem.childElementCount === 1) {
      const maybeButton = elem.children[0] as any;
      if (
        maybeButton.tagName === "BUTTON" &&
        maybeButton.dataset &&
        maybeButton.dataset.page
      ) {
        outpostLinkButtons.push(maybeButton);
      }
    }
  }

  for (const button of outpostLinkButtons) {
    button.removeEventListener("mousedown", nChangePage);
    button.addEventListener("mousedown", (e) => {
      let elem = e.currentTarget as any;
      if (!elem || (e.which !== 1 && e.which !== 2)) {
        return;
      }
      let nTab = e.which === 2;
      if (nTab) {
        e.preventDefault();
      }
      doPageChange(parseInt(button.dataset.page), button.dataset.mod, false);
    });
  }
}
