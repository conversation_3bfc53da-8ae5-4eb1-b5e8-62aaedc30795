import { getCash } from "@/lib/common/character";
import { Inventory, Item } from "@/lib/common/inventory-storage";
import { promptOk } from "@/lib/common/prompt";
import { WebCallManager } from "@/lib/common/webcall";

const SERVICE_CATEGORIES = ["Doctor", "Chef", "Engineer"];

const CACHE_EXPIRATION = 5 * 1000; // 5 seconds

interface MarketCacheEntry {
  data: any;
  timestamp: number;
}
const MARKET_CACHE: Record<string, MarketCacheEntry> = {};

export interface SearchCriteria {
  searchname?: string;
  category?: string;
  tradezone?: number | string;
}

/**
 * can be used in many places
 * data format:
 *  always present:
 *      done: "1"
 *      tradelist_maxresults: "<num>"
 *      then a list of result entries, index starts from 0 and ends with tradelist_maxresults - 1
 *
 *  item results
 *      tradelist_<index>_trade_id: "<id>"
 *      tradelist_<index>_id_member: "<id>"
 *      tradelist_<index>_member_name: "<name>"
 *      tradelist_<index>_id_member_to: "0"
 *      tradelist_<index>_member_to_name: ""
 *      tradelist_<index>_item: "<item>"          this is the "type" of the item, i.e. including stats, name, etc
 *      tradelist_<index>_itemname: "<name>"
 *      tradelist_<index>_price: "<price>"
 *      tradelist_<index>_trade_zone: "<tradezone>"
 *      tradelist_<index>_category: "<category>"
 *      tradelist_<index>_quantity: "<quantity>"
 *      tradelist_<index>_priceper: "<priceper>"
 *      tradelist_<index>_deny_private: "0"
 *
 *  service results
 *      tradelist_<index>_available: "1"
 *      tradelist_<index>_id_member: "<id>"       this is the "trade_id" equivalent of item results. this needs to be put in the buynum field when sending request
 *      tradelist_<index>_level: "<level>"
 *      tradelist_<index>_member_name: "<name>"
 *      tradelist_<index>_price: "<price>"
 *      tradelist_<index>_profession: "<profession>"
 *      tradelist_<index>_trade_zone: "<tradezone>"
 *
 */
export async function fetchMarketData(
  searchCriteria: SearchCriteria | Item
): Promise<Record<string, any>> {
  if (searchCriteria instanceof Item) {
    searchCriteria = {
      searchname: searchCriteria.data["name"],
      category: searchCriteria.marketCategory,
    };
  }

  if (!searchCriteria.searchname && !searchCriteria.category) {
    throw new Error("fetchMarketData: no search criteria");
  }

  // lookup cache
  const cacheKey = JSON.stringify(searchCriteria);
  const cachedData = MARKET_CACHE[cacheKey];
  if (cachedData) {
    if (Date.now() - cachedData.timestamp < CACHE_EXPIRATION) {
      return cachedData.data;
    }
  }

  const dataArr = {
    pagetime: userVars["pagetime"],
    tradezone: searchCriteria.tradezone ?? userVars["DFSTATS_df_tradezone"],
    searchname: searchCriteria.searchname,
    memID: "",
  };

  if (SERVICE_CATEGORIES.includes(searchCriteria.category)) {
    dataArr["profession"] = searchCriteria.category;
    dataArr["category"] = "";
    dataArr["searchtype"] = "buyinglist";
    dataArr["search"] = "services";
  } else {
    dataArr["profession"] = "";
    dataArr["category"] = searchCriteria.category;

    if (searchCriteria.searchname) {
      dataArr["searchtype"] = "buyinglistitemname";
    }
    if (searchCriteria.category) {
      dataArr["searchtype"] = "buyinglistcategory";
    }
    if (searchCriteria.searchname && searchCriteria.category) {
      dataArr["searchtype"] = "buyinglistcategoryitemname";
    }
    dataArr["search"] = "trades";
  }

  const dataStr = await WebCallManager.runWebCall("trade_search", dataArr);
  const data = flshToArr(dataStr, "") as any;
  MARKET_CACHE[cacheKey] = {
    data,
    timestamp: Date.now(),
  };
  return data;
}

export interface ServiceInfo {
  price: number;
  buynum: string;
}
export async function fetchServicePrices(
  category: string
): Promise<Record<string, ServiceInfo>> {
  const data = await fetchMarketData({ category });

  const result: Record<string, ServiceInfo> = {};

  for (let i = 0; i < parseInt(data["tradelist_maxresults"]); i++) {
    const level = parseInt(data[`tradelist_${i}_level`]);
    const price = parseInt(data[`tradelist_${i}_price`]);
    const buynum = data[`tradelist_${i}_id_member`];
    if (!result[level] || price < result[level].price) {
      result[level] = {
        price,
        buynum,
      };
    }
  }

  return result;
}

export async function getCheapestService(
  level: number,
  category?: string,
  servicePrices?: Record<string, ServiceInfo>
): Promise<ServiceInfo | null> {
  if (!category && !servicePrices) {
    throw new Error("getCheapestService: no category and no servicePrices");
  }

  if (!servicePrices) {
    servicePrices = await fetchServicePrices(category);
  }

  let minPrice = Infinity;
  let info = null;
  for (const serviceLevel in servicePrices) {
    if (
      parseInt(serviceLevel) >= level &&
      servicePrices[serviceLevel].price < minPrice
    ) {
      minPrice = servicePrices[serviceLevel].price;
      info = servicePrices[serviceLevel];
    }
  }
  return info;
}

export async function buyService(
  // this is the market category of the service
  category: string,
  buynum: string,
  price: number | string,
  // item to use for service
  itemtype: string,
  itemSlot: number | string
): Promise<any> {
  const action = {
    food: "buycook",
    medical: "buyadminister",
    armour: "buyrepair",
  }[category];
  if (!action) {
    throw new Error(`buyService: unknown category ${category}`);
  }

  if (getCash() < parseInt(price as any)) {
    promptOk("Unable to buy service: Not enough cash");
    return;
  }

  const dataArr = {};

  dataArr["creditsnum"] = 0;
  dataArr["renameto"] = "undefined`undefined";
  dataArr["buynum"] = buynum;
  dataArr["expected_itemprice"] = price.toString();
  dataArr["expected_itemtype2"] = "";
  dataArr["expected_itemtype"] = itemtype;
  dataArr["itemnum2"] = "0";
  dataArr["itemnum"] = itemSlot.toString();
  dataArr["price"] = scrapAmount(itemtype, 1);
  dataArr["action"] = action;

  const results = await WebCallManager.runWebCall("inventory_new", dataArr);

  updateIntoArr(flshToArr(results, "DFSTATS_") as any, userVars);
  populateInventory();
  populateCharacterInventory();
  updateAllFields();

  return results;
}

// if successfully bought, returns the item slot number
export async function buyItem(
  tradeId: string,
  price: number | string
): Promise<number | null> {
  if (!Inventory.hasFreeSpace()) {
    promptOk("Unable to buy item: Inventory is full");
    return null;
  }

  if (getCash() < parseInt(price as any)) {
    promptOk("Unable to buy item: Not enough cash");
    return null;
  }

  const dataArray = {};
  dataArray["creditsnum"] = "undefined";
  dataArray["buynum"] = tradeId;
  dataArray["renameto"] = "undefined`undefined";
  dataArray["expected_itemprice"] = price.toString();
  dataArray["expected_itemtype2"] = "";
  dataArray["expected_itemtype"] = "";
  dataArray["itemnum2"] = 0;
  dataArray["itemnum"] = 0;
  dataArray["price"] = 0;
  dataArray["action"] = "newbuy";

  const results = await WebCallManager.runWebCall("inventory_new", dataArray);

  if (results === "error=missed") {
    promptOk("You have missed this item.");
    return null;
  }

  const flshArr = flshToArr(results, "DFSTATS_") as any;
  let returnSlot: number = null;
  for (let key in flshArr) {
    if (key.startsWith("DFSTATS_df_inv") && key.endsWith("_type")) {
      returnSlot = parseInt(key.split("_")[2].substring(3));
      break;
    }
  }
  updateIntoArr(flshArr, userVars);

  populateInventory();
  updateAllFields();

  return returnSlot;
}
