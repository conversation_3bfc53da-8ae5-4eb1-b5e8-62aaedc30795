var pData = {};

var lockEnterKey = false;
let beggarButtons = [];
let keepBeggarOpen = false;

function initBank(flashErl) {
  flshToArr(flashErl, "", setUserVars);

  df_prompt = document.getElementById("gamecontent");
  df_prompt.tabIndex = "0";

  document
    .getElementById("deposit")
    .addEventListener("keydown", function (evt) {
      if (evt.keyCode === 13 && !lockEnterKey) {
        lockEnterKey = true;
        deposit(0);
      }
    });
  document.getElementById("deposit").addEventListener("keyup", function (evt) {
    if (evt.keyCode === 13 && lockEnterKey) {
      lockEnterKey = false;
    }
  });

  document
    .getElementById("withdraw")
    .addEventListener("keydown", function (evt) {
      if (evt.keyCode === 13 && !lockEnterKey) {
        lockEnterKey = true;
        withdraw(0);
      }
    });
  document.getElementById("withdraw").addEventListener("keyup", function (evt) {
    if (evt.keyCode === 13 && lockEnterKey) {
      lockEnterKey = false;
    }
  });

  var waitForSetup = setInterval(function () {
    if (hrV !== 0) {
      clearInterval(waitForSetup);
      var params = {};
      params["sc"] = userVars["sc"];
      params["userID"] = userVars["userID"];
      params["password"] = userVars["password"];
      webCall("get_values", params, function (data) {
        flshToArr(data, "", function (bnkArr) {
          pData = bnkArr;
          var isPO = true;
          var validOutposts = [
            "Dogg's Stockade",
            "Precinct 13",
            "Fort Pastor",
            "Secronom Bunker",
            "Camp Valcrest",
          ];
          if (
            pData["df_tradezone"] === "4" &&
            validOutposts.indexOf(pData["df_minioutpostname"]) !== -1
          ) {
            isPO = false;
          }
          if (
            pData["df_minioutpost"] === "1" &&
            parseInt(pData["df_tradezone"]) < 10 &&
            isPO
          ) {
            document.getElementById("bank").style.backgroundImage =
              "hotrods/hotrods_v" + hrV + "/HTML5/images/bank/po.png";
            document.getElementById("descText").textContent = "";
          }
          pData["df_cash"] = parseInt(pData["df_cash"]);
          pData["df_bankcash"] = parseInt(pData["df_bankcash"]);
          setupBank();
        });
      });
    }
  }, 10);

  let beggarController = document.createElement("div");
  beggarController.id = "beggarController";
  beggarController.addEventListener("mouseenter", function () {
    beggarController.classList.add("hovered");
  });
  beggarController.addEventListener("mouseleave", function () {
    if (!keepBeggarOpen) {
      beggarController.classList.remove("hovered");
    }
  });

  let beggarTextBox = document.createElement("div");
  beggarTextBox.classList.add("desc");
  beggarTextBox.textContent =
    "You notice a beggar out of the corner of your eye, he's clearly not doing well and is down on his luck. His clothes seem ragged and his health appears to be declining. A few dollars may help him stay alive.";
  beggarController.appendChild(beggarTextBox);

  let beggar10 = document.createElement("button");
  beggar10.textContent = "give $10";
  beggar10.dataset.amt = 10;
  beggarButtons.push(beggar10);

  let beggar100 = document.createElement("button");
  beggar100.textContent = "give $100";
  beggar100.dataset.amt = 100;
  beggarButtons.push(beggar100);

  let beggar1000 = document.createElement("button");
  beggar1000.textContent = "give $1,000";
  beggar1000.dataset.amt = 1000;
  beggarButtons.push(beggar1000);

  let beggar10000 = document.createElement("button");
  beggar10000.textContent = "give $10,000";
  beggar10000.dataset.amt = 10000;
  beggarButtons.push(beggar10000);

  let beggarGrid = document.createElement("div");
  beggarGrid.classList.add("grid22");
  for (let bBtn of beggarButtons) {
    bBtn.addEventListener("click", giveToBeggar);
    beggarGrid.appendChild(bBtn);
  }
  beggarController.appendChild(beggarGrid);
  let bankHolder = document.getElementById("bank");
  bankHolder.appendChild(beggarController);
}

function setupBank() {
  //pData["df_cash"] = ***********;
  //pData["df_bankcash"] = ***********;
  var cash = "Cash: $" + nf.format(pData["df_cash"]);
  var bank = "Bank: $" + nf.format(pData["df_bankcash"]);
  updateCashElements("heldCash", cash);

  let depositElem = document.getElementById("deposit");
  depositElem.value = 0;
  depositElem.max = pData["df_cash"];
  document.getElementById("bankCash").textContent = bank;
  document.getElementById("bankCash").dataset.cash = bank;
  let withdrawElem = document.getElementById("withdraw");
  withdrawElem.value = 0;
  withdrawElem.max = pData["df_bankcash"];
  pageLock = false;

  for (let bBtn of beggarButtons) {
    if (parseInt(pData["df_cash"]) < parseInt(bBtn.dataset.amt)) {
      bBtn.disabled = true;
    } else {
      bBtn.disabled = false;
    }
  }

  lockInput(0);
  lockInput(1);
}

function lockInput(type) {
  var val = 0;
  if (type) {
    // 1 is withdraw
    let elem = document.getElementById("withdraw");
    val = parseInt(elem.value);
    if (val > 0 && val <= parseInt(pData["df_bankcash"])) {
      document.getElementById("wBtn").disabled = false;
    } else {
      document.getElementById("wBtn").disabled = true;
    }
  } else {
    let elem = document.getElementById("deposit");
    val = parseInt(elem.value);
    if (val > 0 && val <= parseInt(pData["df_cash"])) {
      document.getElementById("dBtn").disabled = false;
    } else {
      document.getElementById("dBtn").disabled = true;
    }
  }
}

function deposit(all) {
  if (!pageLock) {
    pageLock = true;
    var amt = 0;
    if (all) {
      amt = *************;
    } else {
      amt = parseInt(document.getElementById("deposit").value);
    }
    playSound("bank");
    if (amt > 0) {
      var params = {};
      params["deposit"] = amt;
      params["sc"] = userVars["sc"];
      params["userID"] = userVars["userID"];
      params["password"] = userVars["password"];
      webCall("bank", params, function (data) {
        updateIntoArr(flshToArr(data, ""), pData);
        setupBank();
      });
    } else {
      pageLock = false;
    }
  }
}

function withdraw(all) {
  if (!pageLock) {
    pageLock = true;
    var amt = 0;
    if (all) {
      amt = 100000000000000000;
    } else {
      amt = parseInt(document.getElementById("withdraw").value);
    }
    playSound("bank");
    if (amt > 0) {
      var params = {};
      params["withdraw"] = amt;
      params["sc"] = userVars["sc"];
      params["userID"] = userVars["userID"];
      params["password"] = userVars["password"];
      webCall("bank", params, function (data) {
        updateIntoArr(flshToArr(data, ""), pData);
        setupBank();
      });
    }
  }
}

function giveToBeggar(evt) {
  if (!pageLock) {
    keepBeggarOpen = true;
    for (let bBtn of beggarButtons) {
      bBtn.disabled = true;
    }
    let amt = parseInt(evt.currentTarget.dataset.amt);
    if (amt > 0) {
      playSound("bank");
      promptLoading("thank you...");

      let dataArr = {};
      dataArr["donate"] = amt;
      dataArr["sc"] = userVars["sc"];
      dataArr["userID"] = userVars["userID"];
      dataArr["password"] = userVars["password"];
      webCall("bank", dataArr, (data) => {
        updateIntoArr(flshToArr(data, ""), pData);
        setTimeout(() => {
          promptEnd();
          setupBank();
          keepBeggarOpen = false;
        }, 1000);
      });
    }
  }
}
