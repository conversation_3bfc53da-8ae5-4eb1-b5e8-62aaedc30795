<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"https://www.w3.org/TR/html4/loose.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta property="og:title" content="Dead Frontier"/>
        <meta property="og:description" content="Dead Frontier is the ultimate 3D survival horror MMORPG. Log in and play with thousands of real players from around the globe as you struggle for survival in a zombie infested city. "/>
        <meta property="og:image" content="https://files.deadfrontier.com/deadfrontier/DF3Dimages/fbzombies.jpg"/>
        <meta property="og:video" content="https://www.youtube.com/v/d8ej0I8IjzM"/>
        <meta property="og:video:width" content="640"/>
        <meta property="og:video:height" content="385"/>
        <meta property="og:video:type" content="application/x-shockwave-flash"/>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
        <meta name="description" content="Dead Frontier Online takes you to a post zombie apocalypse world. Create your character, buy equipment, kill undead and raid malls. Play this FREE ZOMBIE MMO now!"/>
        <meta name="keywords" content="zombie, mmo, zombies, mmorpg, game, games, online, dead, frontier"/>
        <script language="JavaScript" type="text/javascript" src="https://fairview.deadfrontier.com/onlinezombiemmo/Themes/default/script.js"></script>
        <script language="JavaScript" type="text/javascript">
            <!-- // --><![CDATA[
            var smf_theme_url = "https://fairview.deadfrontier.com/onlinezombiemmo/Themes/deadfrontier";
            var smf_images_url = "https://files.deadfrontier.com/deadfrontier/images";
            var smf_scripturl = "https://fairview.deadfrontier.com/onlinezombiemmo/index.php";
            var smf_iso_case_folding = false;
            var smf_charset = "UTF-8";
            // ]]>

            var x4j = [{
                id: 1,
                letter: 'A',
                code: '.-'
            }, {
                id: 2,
                letter: 'B',
                code: '-...'
            }, {
                id: 3,
                letter: 'C',
                code: '-.-.'
            }, {
                id: 4,
                letter: 'D',
                code: '-..'
            }, {
                id: 5,
                letter: 'E',
                code: '.'
            }, {
                id: 6,
                letter: 'F',
                code: '..-.'
            }, {
                id: 7,
                letter: 'G',
                code: '--.'
            }, {
                id: 8,
                letter: 'H',
                code: '....'
            }, {
                id: 9,
                letter: 'I',
                code: '..'
            }, {
                id: 10,
                letter: 'J',
                code: '.---'
            }, {
                id: 11,
                letter: 'K',
                code: '-.-'
            }, {
                id: 12,
                letter: 'L',
                code: '.-..'
            }, {
                id: 13,
                letter: 'M',
                code: '--'
            }, {
                id: 14,
                letter: 'N',
                code: '-.'
            }, {
                id: 15,
                letter: 'O',
                code: '---'
            }, {
                id: 16,
                letter: 'P',
                code: '.--.'
            }, {
                id: 17,
                letter: 'Q',
                code: '--.-'
            }, {
                id: 18,
                letter: 'R',
                code: '.-.'
            }, {
                id: 19,
                letter: 'S',
                code: '...'
            }, {
                id: 20,
                letter: 'T',
                code: '-'
            }, {
                id: 21,
                letter: 'U',
                code: '..-'
            }, {
                id: 22,
                letter: 'V',
                code: '...-'
            }, {
                id: 23,
                letter: 'W',
                code: '.--'
            }, {
                id: 24,
                letter: 'X',
                code: '-..-'
            }, {
                id: 25,
                letter: 'Y',
                code: '-.--'
            }, {
                id: 26,
                letter: 'Z',
                code: '--..'
            }, {
                id: 27,
                letter: '1',
                code: '.----'
            }, {
                id: 28,
                letter: '2',
                code: '..---'
            }, {
                id: 29,
                letter: '3',
                code: '...--'
            }, {
                id: 30,
                letter: '4',
                code: '....-'
            }, {
                id: 31,
                letter: '5',
                code: '.....'
            }, {
                id: 32,
                letter: '6',
                code: '-....'
            }, {
                id: 33,
                letter: '7',
                code: '--...'
            }, {
                id: 34,
                letter: '8',
                code: '---..'
            }, {
                id: 35,
                letter: '9',
                code: '----.'
            }, {
                id: 36,
                letter: '0',
                code: '-----'
            }];
            function x4jC(c) {
                var ret = '';
                for (var i = 0; i < c.toUpperCase().length; i++) {
                    for (var j = 0; j < x4j.length; j++) {
                        if (x4j[j].letter === c.toUpperCase()[i]) {
                            ret = ret + x4j[j].code + ' ';
                        }
                    }
                }
                return ret;
            }
        </script>
        <script type="text/javascript" src="https://files.deadfrontier.com/deadfrontier/swfobject/swfobject.js"></script>
        <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.4/jquery.min.js"></script>
        <!-- <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script> 
	<script type="text/javascript" src="https://code.jquery.com/jquery-migrate-1.4.1.min.js"></script>  -->
        <script type="text/javascript" src="https://files.deadfrontier.com/deadfrontier/fancybox/jquery.fancybox-1.3.4.pack.js"></script>
        <script type="text/javascript" src="https://files.deadfrontier.com/deadfrontier/fancybox/jquery.easing-1.4.pack.js"></script>
        <link rel="stylesheet" href="https://files.deadfrontier.com/deadfrontier/fancybox/jquery.fancybox-1.3.4.css" type="text/css" media="screen"/>
        <title>Dead Frontier Online Zombie MMO - Outpost</title>
        <script type="text/javascript">
            $(document).ready(function() {
                $("#DFHelpBox").fancybox({
                    "overlayShow": true,
                    "overlayColor": "#000000",
                    "showCloseButton": true,
                    "hideOnContentClick": true,
                    "width": 530,
                    "height": 445
                });

            });
        </script>
        <LINK REL="SHORTCUT ICON" HREF="https://files.deadfrontier.com/deadfrontier/favicon.ico">
        <link rel="stylesheet" type="text/css" href="https://fairview.deadfrontier.com/onlinezombiemmo/Themes/deadfrontier/style.css"/>
        <link rel="stylesheet" type="text/css" href="https://fairview.deadfrontier.com/onlinezombiemmo/Themes/default/print.css" media="print"/>
        <script type='text/javascript'>

            var _gaq = _gaq || [];
            _gaq.push(['_setAccount', 'UA-207118-4']);
            _gaq.push(['_setDomainName', '.deadfrontier.com']);
            _gaq.push(['_trackPageview']);

            (function() {
                var ga = document.createElement('script');
                ga.type = 'text/javascript';
                ga.async = true;
                ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'https://www') + '.google-analytics.com/ga.js';
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(ga, s);
            }
            )();
        </script>
    </head>
    <script>
        <!--
        function wopen(url, name, w, h) {
            // Fudge factors for window decoration space.
            // In my tests these work well on all platforms & browsers.
            w += 32;
            h += 96;
            var win = window.open(url, name, 'width=' + w + ', height=' + h + ', ' + 'location=no, menubar=no, ' + 'status=no, toolbar=no, scrollbars=no, resizable=no');
            win.resizeTo(w, h);
            win.focus();
        }
        // -->

        //parent.change_parent_url(document.location);

        function UpdateFlash(direction) {
            var objects = document.getElementsByTagName('OBJECT');
            for (var i = 0; i < objects.length; i++) {
                objects[i].width = objects[i].width - (0.1);
            }

            var objects = document.getElementsByTagName('EMBED');
            for (var i = 0; i < objects.length; i++) {
                objects[i].width = objects[i].width - (0.1);
            }
        }

        //TimeOut2 = window.setTimeout('UpdateFlash(1);',100);
        //TimeOut3 = window.setTimeout('UpdateFlash(-1);',1000);
        //TimeOut4 = window.setTimeout('UpdateFlash(1);',3000);

        // Browser Window Size and Position
        // copyright Stephen Chapman, 3rd Jan 2005, 8th Dec 2005
        // you may copy these functions but please keep the copyright notice as well
        function pageWidth() {
            return window.innerWidth != null ? window.innerWidth : document.documentElement && document.documentElement.clientWidth ? document.documentElement.clientWidth : document.body != null ? document.body.clientWidth : null;
        }
        function pageHeight() {
            return window.innerHeight != null ? window.innerHeight : document.documentElement && document.documentElement.clientHeight ? document.documentElement.clientHeight : document.body != null ? document.body.clientHeight : null;
        }
        function posLeft() {
            return typeof window.pageXOffset != 'undefined' ? window.pageXOffset : document.documentElement && document.documentElement.scrollLeft ? document.documentElement.scrollLeft : document.body.scrollLeft ? document.body.scrollLeft : 0;
        }
        function posTop() {
            return typeof window.pageYOffset != 'undefined' ? window.pageYOffset : document.documentElement && document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop ? document.body.scrollTop : 0;
        }
        function posRight() {
            return posLeft() + pageWidth();
        }
        function posBottom() {
            return posTop() + pageHeight();
        }
    </script>
    <style>
        table.design2010 {
            border-spacing: 0px;
            border-style: none;
            border-width: 0px;
            border-spacing: 0px;
            cell-spacing: 0px;
            cell-padding: 0px;
            overflow: hidden;
        }

        td.design2010 {
            padding: 0px;
            font-size: 12px;
            color: #e2b598;
            font-family: "Lucida Sans Unicode", sans-serif;
        }

        a {
            text-decoration: none;
        }

        img {
            border-style: none;
            border-width: 0px;
        }
    </style>
    <!--onLoad="setTimeout('window.scrollBy(1,1)',1000); setTimeout('window.scrollBy(1,1)',5000);"-->
    <body bgcolor="#000000" link="#981313" vlink="#981313" alink="#981313" LEFTMARGIN=0 TOPMARGIN=0 RIGHTMARGIN=0 BOTTOMMARGIN=0 MARGINWIDTH=0 MARGINHEIGHT=0>
</table>
<table class="design2010" align="center" width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
    <tr height="841">
        <td class="design2010" width="50%" style="background-image: url('https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/left_margin.jpg');
                             background-attachment: scroll;
                             background-position: top right;
                             background-repeat: no-repeat;"></td>
        <td class="design2010" width="985" valign="top">
            <table class="design2010" border="0" width="985" align="center" cellpadding="0" cellspacing="0">
                <tr height="228">
                    <td width="985" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/header.jpg" style="background-repeat: no-repeat;" align=left valign="bottom">
                        <!---->
                        <div width="500" height="100">
                            <iframe src="https://www.facebook.com/plugins/like.php?href=https://www.facebook.com/OfficialDeadFrontier/&width&layout=button_count&action=like&show_faces=false&share=true&height=35&appId=" frameBorder="0" width="150" height="25">
                        </div>
</iframe><br>
<br>
<br>
</td></tr>
<tr>
    <td class="design2010">
        <table class="design2010" border="0" width="985" align="center" cellpadding="0" cellspacing="0">
            <tr height="597">
                <td width="35" class="design2010" style="background: url(https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/extend_left_edge.jpg) repeat-y;" valign="top">
                    <table width="35" class="design2010" border="0" width="985" align="center" cellpadding="0" cellspacing="0">
                        <tr height="597">
                            <td width="35" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/left_edge.jpg"></td>
                        </tr>
                    </table>
                </td>
                <td width="911" class="design2010" valign="top">
                    <table class="design2010" border="0" width="911" align="center" cellpadding="0" cellspacing="0">
                        <tr height="25">
                            <td width="911" class="design2010" align="center" valign="top">
                                <div style=" " id="block13">
                                    <table class="design2010" border="0" width="911" align="center" cellpadding="0" cellspacing="0">
                                        <tr height="25">
                                            <td width="911" class="design2010">
                                                <!-- main menu goes here -->
                                                <table class="design2010" border="0" width="911" align="center" cellpadding="0" cellspacing="0">
                                                    <tr id="outpostnavigationheaders" height="25">
                                                        <td width="23" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_left.jpg"></td>
                                                        <!-- outpost button goes here -->
                                                        <td width="153" class="design2010">
                                                            <a href="index.php">
                                                                <img name="outpost" src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_outpost.jpg" onmouseover="document['outpost'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_outpost_over.jpg';" onmouseout="document['outpost'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_outpost.jpg';">
                                                            </a>
                                                        </td>
                                                        <td width="19" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_div1.jpg"></td>
                                                        <!-- creditshop button goes here -->
                                                        <td width="89" class="design2010">
                                                            <a href="index.php?page=28">
                                                                <img name="creditshop" src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_creditshop.jpg" onmouseover="document['creditshop'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_creditshop_over.jpg';" onmouseout="document['creditshop'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_creditshop.jpg';">
                                                            </a>
                                                        </td>
                                                        <td width="17" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_div2.jpg"></td>
                                                        <!-- forum button goes here -->
                                                        <td width="56" class="design2010">
                                                            <a href="index.php?action=forum">
                                                                <img name="forum" src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_forum.jpg" onmouseover="document['forum'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_forum_over.jpg';" onmouseout="document['forum'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_forum.jpg';">
                                                            </a>
                                                        </td>
                                                        <td width="13" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_div3.jpg"></td>
                                                        <!-- wiki button goes here -->
                                                        <td width="44" class="design2010">
                                                            <a href="https://deadfrontier.fandom.com/wiki/Dead_Frontier_Wiki" target="_blank">
                                                                <img name="wiki" src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_wiki.jpg" onmouseover="document['wiki'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_wiki_over.jpg';" onmouseout="document['wiki'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_wiki.jpg';">
                                                            </a>
                                                        </td>
                                                        <td width="14" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_div4.jpg"></td>
                                                        <!-- intro button goes here -->
                                                        <td width="53" class="design2010">
                                                            <a href="index.php?page=43">
                                                                <img name="intro" src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_intro.jpg" onmouseover="document['intro'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_intro_over.jpg';" onmouseout="document['intro'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_intro.jpg';">
                                                            </a>
                                                        </td>
                                                        <td width="14" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_div5.jpg"></td>
                                                        <!-- rules button goes here -->
                                                        <td width="49" class="design2010">
                                                            <a href="#" onclick="window.open('https://www.deadfrontier.com/rules.html','rules','width=1000,height=750, scrollbars=yes')">
                                                                <img name="rules" src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_rules.jpg" onmouseover="document['rules'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_rules_over.jpg';" onmouseout="document['rules'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_rules.jpg';">
                                                            </a>
                                                        </td>
                                                        <td width="163" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_div6.jpg"></td>
                                                        <!-- help button goes here -->
                                                        <td width="72" class="design2010">
                                                            <a href="index.php?page=53">
                                                                <img name="help" src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_help.jpg" onmouseover="document['help'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_help_over.jpg';" onmouseout="document['help'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_help.jpg';">
                                                            </a>
                                                        </td>
                                                        <td width="13" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_div7.jpg"></td>
                                                        <!-- logout button goes here -->
                                                        <td width="90" class="design2010">
                                                            <a href="index.php?action=logout;sesc=ca30f254f9b412cad1c84fdd895fa866">
                                                                <img name="logout" src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_logout.jpg" onmouseover="document['logout'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_logout_over.jpg';" onmouseout="document['logout'].src = 'https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_logout.jpg';">
                                                            </a>
                                                        </td>
                                                        <td width="29" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_right.jpg"></td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr height="24">
                                            <td width="911" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/menu_bottom.jpg"></td>
                                        </tr>
                                        <tr height="100%">
                                            <td width="911" class="design2010">
                                                <table class="design2010" border="0" width="911" align="center" cellpadding="0" cellspacing="0">
                                                    <tr height="100%">
                                                        <td width="202" class="design2010" valign="top">
                                                            <table class="design2010" border="0" width="202" align="center" cellpadding="0" cellspacing="0">
                                                                <tr height="548">
                                                                    <td width="15" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/stats_left.jpg"></td>
                                                                    <td width="175" class="design2010" valign="top" align="center" style="background-image: url('https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/stats_bottom.jpg');
                                                                     background-attachment: scroll;
                                                                     background-position: bottom center;
                                                                     background-repeat: no-repeat;">
                                                                        <!-- left stats bar goes here -->
                                                                        <script type='text/javascript' src='hotrods/hotrods_v9_0_11/HTML5/js/classList.js'></script>
                                                                        <script type='text/javascript' src='hotrods/hotrods_v9_0_11/HTML5/js/dataset.js'></script>
                                                                        <script type='text/javascript' src='hotrods/hotrods_v9_0_11/HTML5/js/base.js'></script>
                                                                        <div id='htmlFlashReplace'>
                                                                            <div id='sidebar'>
                                                                                <script>
                                                                                    loadSidebar('sc=ca30f254f9b412cad1c84fdd895fa866&userID=********&template_ID=0&password=02c5eb23839347bfed157e6277364004735bad2e&temp_time=*********&server_time=*********&checkstuff=0&connection=dfflashconn7371&hidebuttons=0&imgver=97&DFSTATS_id_member=********&DFSTATS_id_character=1&DFSTATS_df_name=zys5945&DFSTATS_df_gender=Male&DFSTATS_df_rank=Exterminator&DFSTATS_df_profession=Investigator&DFSTATS_df_refid=0&DFSTATS_df_ahead=3&DFSTATS_df_abody=5&DFSTATS_df_alegs=6&DFSTATS_df_level=123&DFSTATS_df_maxachievedlevel=0&DFSTATS_df_freepoints=0&DFSTATS_df_dead=0&DFSTATS_df_dangerlevel=0&DFSTATS_df_cash=111&DFSTATS_df_bankcash=8503357&DFSTATS_df_credits=0&DFSTATS_df_buyer=1&DFSTATS_df_goldmember=1&DFSTATS_df_goldmembertime=*********&DFSTATS_df_exp=5743116&DFSTATS_df_exptotal=*********&DFSTATS_df_expstart=*********&DFSTATS_df_hpmax=100&DFSTATS_df_hpcurrent=100&DFSTATS_df_hungerhp=71&DFSTATS_df_armourstr=75&DFSTATS_df_armourhp=0&DFSTATS_df_armourhpmax=300&DFSTATS_df_armourname=Exterminator Mesh XT&DFSTATS_df_armourtype=exterminatormeshxt_stats2424&DFSTATS_df_weapon1type=shortfusecrossbow_stats888&DFSTATS_df_weapon1name=Short Fuse Crossbow&DFSTATS_df_weapon1ammo=heavygrenadeammo&DFSTATS_df_weapon2type=goretooth44g_stats888&DFSTATS_df_weapon2name=Goretooth 44G&DFSTATS_df_weapon2ammo=&DFSTATS_df_weapon3type=battleaxe_stats888&DFSTATS_df_weapon3name=Battle Axe&DFSTATS_df_weapon3ammo=&DFSTATS_df_strength=50&DFSTATS_df_accuracy=80&DFSTATS_df_agility=100&DFSTATS_df_endurance=50&DFSTATS_df_criticalhit=88&DFSTATS_df_reloading=100&DFSTATS_df_dexterity=0&DFSTATS_df_survival=25&DFSTATS_df_promelee=135&DFSTATS_df_propistol=10&DFSTATS_df_proshotgun=0&DFSTATS_df_promachinegun=120&DFSTATS_df_proexplosive=21&DFSTATS_df_prorifle=120&DFSTATS_df_positionx=1054&DFSTATS_df_positiony=987&DFSTATS_df_positionz=0&DFSTATS_df_invslots=30&DFSTATS_df_inv1_type=14rifleammo&DFSTATS_df_inv1_quantity=1200&DFSTATS_df_inv2_type=&DFSTATS_df_inv2_quantity=0&DFSTATS_df_inv3_type=&DFSTATS_df_inv3_quantity=0&DFSTATS_df_inv4_type=14rifleammo&DFSTATS_df_inv4_quantity=605&DFSTATS_df_inv5_type=heavygrenadeammo&DFSTATS_df_inv5_quantity=57&DFSTATS_df_inv6_type=127rifleammo&DFSTATS_df_inv6_quantity=564&DFSTATS_df_inv7_type=&DFSTATS_df_inv7_quantity=0&DFSTATS_df_inv8_type=&DFSTATS_df_inv8_quantity=0&DFSTATS_df_inv9_type=&DFSTATS_df_inv9_quantity=0&DFSTATS_df_inv10_type=&DFSTATS_df_inv10_quantity=0&DFSTATS_df_inv11_type=&DFSTATS_df_inv11_quantity=0&DFSTATS_df_inv12_type=&DFSTATS_df_inv12_quantity=0&DFSTATS_df_inv13_type=&DFSTATS_df_inv13_quantity=0&DFSTATS_df_inv14_type=&DFSTATS_df_inv14_quantity=0&DFSTATS_df_inv15_type=&DFSTATS_df_inv15_quantity=0&DFSTATS_df_inv16_type=&DFSTATS_df_inv16_quantity=0&DFSTATS_df_inv17_type=&DFSTATS_df_inv17_quantity=0&DFSTATS_df_inv18_type=&DFSTATS_df_inv18_quantity=0&DFSTATS_df_inv19_type=&DFSTATS_df_inv19_quantity=0&DFSTATS_df_inv20_type=&DFSTATS_df_inv20_quantity=0&DFSTATS_df_inv21_type=&DFSTATS_df_inv21_quantity=0&DFSTATS_df_inv22_type=&DFSTATS_df_inv22_quantity=0&DFSTATS_df_inv23_type=&DFSTATS_df_inv23_quantity=0&DFSTATS_df_inv24_type=&DFSTATS_df_inv24_quantity=0&DFSTATS_df_inv25_type=&DFSTATS_df_inv25_quantity=0&DFSTATS_df_inv26_type=&DFSTATS_df_inv26_quantity=0&DFSTATS_df_inv27_type=&DFSTATS_df_inv27_quantity=0&DFSTATS_df_inv28_type=&DFSTATS_df_inv28_quantity=0&DFSTATS_df_inv29_type=10gaugeammo&DFSTATS_df_inv29_quantity=415&DFSTATS_df_inv30_type=energycellammo&DFSTATS_df_inv30_quantity=1119&DFSTATS_df_hasservice=0&DFSTATS_df_tradezone=21&DFSTATS_df_32ammo=0&DFSTATS_df_35ammo=0&DFSTATS_df_357ammo=0&DFSTATS_df_38ammo=0&DFSTATS_df_40ammo=0&DFSTATS_df_45ammo=0&DFSTATS_df_50ammo=0&DFSTATS_df_55ammo=0&DFSTATS_df_55rifleammo=0&DFSTATS_df_75rifleammo=0&DFSTATS_df_9rifleammo=0&DFSTATS_df_127rifleammo=564&DFSTATS_df_14rifleammo=1805&DFSTATS_df_12gaugeammo=0&DFSTATS_df_16gaugeammo=0&DFSTATS_df_20gaugeammo=0&DFSTATS_df_10gaugeammo=415&DFSTATS_df_heavygrenadeammo=457&DFSTATS_df_grenadeammo=0&DFSTATS_df_fuelammo=0&DFSTATS_df_energycellammo=1119&DFSTATS_df_biomassammo=0&DFSTATS_df_previousposition1=&DFSTATS_df_previousposition2=&DFSTATS_df_previousposition3=&DFSTATS_df_previousposition4=&DFSTATS_df_previousposition5=&DFSTATS_df_previousposition6=&DFSTATS_df_itemgen1_type=&DFSTATS_df_itemgen1_quantity=0&DFSTATS_df_itemgen2_type=&DFSTATS_df_itemgen2_quantity=0&DFSTATS_df_itemgen3_type=&DFSTATS_df_itemgen3_quantity=0&DFSTATS_df_secretshop=0&DFSTATS_df_lastpvp1=0&DFSTATS_df_lastpvp2=0&DFSTATS_df_lastmostdamage1=0&DFSTATS_df_lastmostdamage2=0&DFSTATS_df_lasthitby=0&DFSTATS_df_mostdamageby=0&DFSTATS_df_defensetime=0&DFSTATS_df_defensearea=&DFSTATS_df_defensewins=0&DFSTATS_df_minioutpost=1&DFSTATS_df_minioutpostattack=0&DFSTATS_df_minioutpostname=Secronom Bunker&DFSTATS_df_minioutpostx=1054&DFSTATS_df_minioutposty=987&DFSTATS_df_referrals=0&DFSTATS_df_playerkills=0&DFSTATS_df_expdeath=4498000&DFSTATS_df_expdeathrecord=19674301&DFSTATS_df_expdeathrecord_weekly=19674301&DFSTATS_df_expdeath_weekly=4498000&DFSTATS_df_expdeathrecord_daily=4498000&DFSTATS_df_expdeath_daily=4498000&DFSTATS_df_loots_daily=314&DFSTATS_df_loots_weekly=4824&DFSTATS_df_loots_total=8087&DFSTATS_df_playerkills_weekly=0&DFSTATS_df_playerkills_daily=0&DFSTATS_df_itime=0&DFSTATS_df_bonustime=0&DFSTATS_df_refertime=0&DFSTATS_df_hungertime=556608733&DFSTATS_df_lastspawntime=0&DFSTATS_df_servertime=556608733&DFSTATS_df_creationtime=555553063&DFSTATS_df_buildingexp=0&DFSTATS_df_buildingchanges=0&DFSTATS_df_mission1_title=&DFSTATS_df_mission1_exp=0&DFSTATS_df_mission1_cash=0&DFSTATS_df_mission1_type=&DFSTATS_df_mission1_targetloc=&DFSTATS_df_mission1_targettype=&DFSTATS_df_mission1_targetquantity=0&DFSTATS_df_mission1_stage=0&DFSTATS_df_mission2_title=&DFSTATS_df_mission2_exp=0&DFSTATS_df_mission2_cash=0&DFSTATS_df_mission2_type=&DFSTATS_df_mission2_targetloc=&DFSTATS_df_mission2_targettype=&DFSTATS_df_mission2_targetquantity=0&DFSTATS_df_mission2_stage=0&DFSTATS_df_mission3_title=&DFSTATS_df_mission3_exp=0&DFSTATS_df_mission3_cash=0&DFSTATS_df_mission3_type=&DFSTATS_df_mission3_targetloc=&DFSTATS_df_mission3_targettype=&DFSTATS_df_mission3_targetquantity=0&DFSTATS_df_mission3_stage=0&DFSTATS_df_missions_completed=&DFSTATS_df_mission1_targetloc2=&DFSTATS_df_mission2_targetloc2=&DFSTATS_df_mission3_targetloc2=&DFSTATS_df_campaign_cp=&DFSTATS_df_campaign_date=&DFSTATS_df_campaign_site=&DFSTATS_df_campaign_sid=0&DFSTATS_df_transaction_id=35588&DFSTATS_df_avatar_face=7&DFSTATS_df_avatar_skin_colour=tan&DFSTATS_df_avatar_beard=stubble&DFSTATS_df_avatar_hair=short&DFSTATS_df_avatar_hair_colour=black&DFSTATS_df_avatar_mask=exterminatorhelmet_colourRed&DFSTATS_df_avatar_hat=bobblehat_colourGrey&DFSTATS_df_avatar_shirt=vest_colourWhite&DFSTATS_df_avatar_trousers=combats_colourGrey&DFSTATS_df_avatar_coat=&DFSTATS_df_see_surgeon=0&DFSTATS_df_avatar_weapon1=riflewood&DFSTATS_df_avatar_weapon2=sword&DFSTATS_df_avatar_weapon3=wood&DFSTATS_df_storage_slots=465&DFSTATS_df_looteditem_type=&DFSTATS_df_looteditem_pos=0&DFSTATS_df_looteditem_time=556580791&DFSTATS_df_looteditem_quantity=0&DFSTATS_df_looteditem_event_id=0&DFSTATS_df_gameversion=1&DFSTATS_df_session3d=406&DFSTATS_df_ghostreset=0&DFSTATS_df_prearena_hp=0&DFSTATS_df_prearena_armour=0&DFSTATS_df_boostspeeduntil=0&DFSTATS_df_boostexpuntil=1756057170&DFSTATS_df_boostdamageuntil=1756012507&DFSTATS_df_boostspeeduntil_ex=0&DFSTATS_df_boostdamageuntil_ex=0&DFSTATS_df_boostexpuntil_ex=0&DFSTATS_df_building_type=&DFSTATS_df_building_id=0&DFSTATS_df_building_orientation=0&DFSTATS_df_perm_secretshop=0&DFSTATS_df_clan_id=-1&DFSTATS_df_clan_name=&DFSTATS_df_clan_rank=&DFSTATS_df_clan_secretshop=0&DFSTATS_df_clan_last_join_time=1756066362&DFSTATS_df_implant1_type=quartermasterimplant&DFSTATS_df_implant2_type=prophecyimplant&DFSTATS_df_implant3_type=mimicimplant&DFSTATS_df_implant4_type=mastercraftimplant&DFSTATS_df_implant5_type=runnerimplant&DFSTATS_df_implant6_type=secrolink_nt&DFSTATS_df_implant7_type=volatiledevilimplant&DFSTATS_df_implant8_type=rageimplant&DFSTATS_df_implant9_type=&DFSTATS_df_implant10_type=&DFSTATS_df_implant11_type=&DFSTATS_df_implant12_type=&DFSTATS_df_implant13_type=&DFSTATS_df_implant14_type=&DFSTATS_df_implant15_type=&DFSTATS_df_implant16_type=&DFSTATS_df_implant17_type=&DFSTATS_df_implant18_type=&DFSTATS_df_implant19_type=&DFSTATS_df_implant20_type=&DFSTATS_df_implantslots=8&DFSTATS_df_block_support_until=0&DFSTATS_df_last_daily_tpk_win=0&DFSTATS_df_hidearmour=0&DFSTATS_df_seasonalgifts=0&DFSTATS_df_freestatreset=0&DFSTATS_df_freelevel50statreset=1&DFSTATS_df_fasttravelpermissions=63&DFSTATS_df_backpack=smallbackpack_stats2&DFSTATS_df_lastdailywin=0&DFSTATS_df_secret_shop_cooldown_until=0&DFSTATS_newpms=0&EXPTABLE_exp_lvl2=125&tts=72000&EXPTABLE_exp_lvl3=250&tts=72000&EXPTABLE_exp_lvl4=500&tts=72000&EXPTABLE_exp_lvl5=1000&tts=72000&EXPTABLE_exp_lvl6=1750&tts=72000&EXPTABLE_exp_lvl7=2500&tts=72000&EXPTABLE_exp_lvl8=3250&tts=72000&EXPTABLE_exp_lvl9=4000&tts=72000&EXPTABLE_exp_lvl10=5000&tts=72000&EXPTABLE_exp_lvl11=6000&tts=72000&EXPTABLE_exp_lvl12=8000&tts=72000&EXPTABLE_exp_lvl13=10000&tts=72000&EXPTABLE_exp_lvl14=12000&tts=72000&EXPTABLE_exp_lvl15=14000&tts=72000&EXPTABLE_exp_lvl16=16000&tts=72000&EXPTABLE_exp_lvl17=20000&tts=72000&EXPTABLE_exp_lvl18=24000&tts=72000&EXPTABLE_exp_lvl19=28000&tts=72000&EXPTABLE_exp_lvl20=32000&tts=72000&EXPTABLE_exp_lvl21=36000&tts=72000&EXPTABLE_exp_lvl22=40000&tts=72000&EXPTABLE_exp_lvl23=45000&tts=72000&EXPTABLE_exp_lvl24=50000&tts=72000&EXPTABLE_exp_lvl25=55000&tts=72000&EXPTABLE_exp_lvl26=60000&tts=72000&EXPTABLE_exp_lvl27=70000&tts=72000&EXPTABLE_exp_lvl28=80000&tts=72000&EXPTABLE_exp_lvl29=90000&tts=72000&EXPTABLE_exp_lvl30=100000&tts=72000&EXPTABLE_exp_lvl31=120000&tts=72000&EXPTABLE_exp_lvl32=140000&tts=72000&EXPTABLE_exp_lvl33=160000&tts=72000&EXPTABLE_exp_lvl34=180000&tts=72000&EXPTABLE_exp_lvl35=200000&tts=72000&EXPTABLE_exp_lvl36=220000&tts=72000&EXPTABLE_exp_lvl37=260000&tts=72000&EXPTABLE_exp_lvl38=300000&tts=72000&EXPTABLE_exp_lvl39=350000&tts=72000&EXPTABLE_exp_lvl40=400000&tts=72000&EXPTABLE_exp_lvl41=450000&tts=72000&EXPTABLE_exp_lvl42=500000&tts=72000&EXPTABLE_exp_lvl43=550000&tts=72000&EXPTABLE_exp_lvl44=600000&tts=72000&EXPTABLE_exp_lvl45=650000&tts=72000&EXPTABLE_exp_lvl46=700000&tts=72000&EXPTABLE_exp_lvl47=750000&tts=72000&EXPTABLE_exp_lvl48=800000&tts=72000&EXPTABLE_exp_lvl49=900000&tts=72000&EXPTABLE_exp_lvl50=1000000&tts=72000&EXPTABLE_exp_lvl51=1100000&tts=72000&EXPTABLE_exp_lvl52=1200000&tts=72000&EXPTABLE_exp_lvl53=1300000&tts=72000&EXPTABLE_exp_lvl54=1400000&tts=72000&EXPTABLE_exp_lvl55=1500000&tts=72000&EXPTABLE_exp_lvl56=1600000&tts=72000&EXPTABLE_exp_lvl57=1700000&tts=72000&EXPTABLE_exp_lvl58=1800000&tts=72000&EXPTABLE_exp_lvl59=1900000&tts=72000&EXPTABLE_exp_lvl60=2000000&tts=72000&EXPTABLE_exp_lvl61=2100000&tts=72000&EXPTABLE_exp_lvl62=2200000&tts=72000&EXPTABLE_exp_lvl63=2300000&tts=72000&EXPTABLE_exp_lvl64=2400000&tts=72000&EXPTABLE_exp_lvl65=2500000&tts=72000&EXPTABLE_exp_lvl66=2600000&tts=72000&EXPTABLE_exp_lvl67=2700000&tts=72000&EXPTABLE_exp_lvl68=2800000&tts=72000&EXPTABLE_exp_lvl69=2900000&tts=72000&EXPTABLE_exp_lvl70=3000000&tts=72000&EXPTABLE_exp_lvl71=3100000&tts=72000&EXPTABLE_exp_lvl72=3200000&tts=72000&EXPTABLE_exp_lvl73=3300000&tts=72000&EXPTABLE_exp_lvl74=3400000&tts=72000&EXPTABLE_exp_lvl75=3500000&tts=72000&EXPTABLE_exp_lvl76=3600000&tts=72000&EXPTABLE_exp_lvl77=3700000&tts=72000&EXPTABLE_exp_lvl78=3800000&tts=72000&EXPTABLE_exp_lvl79=3900000&tts=72000&EXPTABLE_exp_lvl80=4000000&tts=72000&EXPTABLE_exp_lvl81=4100000&tts=72000&EXPTABLE_exp_lvl82=4200000&tts=72000&EXPTABLE_exp_lvl83=4300000&tts=72000&EXPTABLE_exp_lvl84=4400000&tts=72000&EXPTABLE_exp_lvl85=4500000&tts=72000&EXPTABLE_exp_lvl86=4600000&tts=72000&EXPTABLE_exp_lvl87=4700000&tts=72000&EXPTABLE_exp_lvl88=4800000&tts=72000&EXPTABLE_exp_lvl89=4900000&tts=72000&EXPTABLE_exp_lvl90=5000000&tts=72000&EXPTABLE_exp_lvl91=5100000&tts=72000&EXPTABLE_exp_lvl92=5200000&tts=72000&EXPTABLE_exp_lvl93=5300000&tts=72000&EXPTABLE_exp_lvl94=5400000&tts=72000&EXPTABLE_exp_lvl95=5500000&tts=72000&EXPTABLE_exp_lvl96=5600000&tts=72000&EXPTABLE_exp_lvl97=5700000&tts=72000&EXPTABLE_exp_lvl98=5800000&tts=72000&EXPTABLE_exp_lvl99=5900000&tts=72000&EXPTABLE_exp_lvl100=6000000&tts=72000&EXPTABLE_exp_lvl101=6100000&tts=72000&EXPTABLE_exp_lvl102=6200000&tts=72000&EXPTABLE_exp_lvl103=6300000&tts=72000&EXPTABLE_exp_lvl104=6400000&tts=72000&EXPTABLE_exp_lvl105=6500000&tts=72000&EXPTABLE_exp_lvl106=6600000&tts=72000&EXPTABLE_exp_lvl107=6700000&tts=72000&EXPTABLE_exp_lvl108=6800000&tts=72000&EXPTABLE_exp_lvl109=6900000&tts=72000&EXPTABLE_exp_lvl110=7000000&tts=72000&EXPTABLE_exp_lvl111=7000000&tts=72000&EXPTABLE_exp_lvl112=7000000&tts=72000&EXPTABLE_exp_lvl113=7000000&tts=72000&EXPTABLE_exp_lvl114=7000000&tts=72000&EXPTABLE_exp_lvl115=7000000&tts=72000&EXPTABLE_exp_lvl116=7000000&tts=72000&EXPTABLE_exp_lvl117=7000000&tts=72000&EXPTABLE_exp_lvl118=7000000&tts=72000&EXPTABLE_exp_lvl119=7000000&tts=72000&EXPTABLE_exp_lvl120=7000000&tts=72000&EXPTABLE_exp_lvl121=7000000&tts=72000&EXPTABLE_exp_lvl122=7000000&tts=72000&EXPTABLE_exp_lvl123=7000000&tts=72000&EXPTABLE_exp_lvl124=7000000&tts=72000&EXPTABLE_exp_lvl125=7000000&tts=72000&EXPTABLE_exp_lvl126=7000000&tts=72000&EXPTABLE_exp_lvl127=7000000&tts=72000&EXPTABLE_exp_lvl128=7000000&tts=72000&EXPTABLE_exp_lvl129=7000000&tts=72000&EXPTABLE_exp_lvl130=7000000&tts=72000&EXPTABLE_exp_lvl131=7000000&tts=72000&EXPTABLE_exp_lvl132=7000000&tts=72000&EXPTABLE_exp_lvl133=7000000&tts=72000&EXPTABLE_exp_lvl134=7000000&tts=72000&EXPTABLE_exp_lvl135=7000000&tts=72000&EXPTABLE_exp_lvl136=7000000&tts=72000&EXPTABLE_exp_lvl137=7000000&tts=72000&EXPTABLE_exp_lvl138=7000000&tts=72000&EXPTABLE_exp_lvl139=7000000&tts=72000&EXPTABLE_exp_lvl140=7000000&tts=72000&EXPTABLE_exp_lvl141=7000000&tts=72000&EXPTABLE_exp_lvl142=7000000&tts=72000&EXPTABLE_exp_lvl143=7000000&tts=72000&EXPTABLE_exp_lvl144=7000000&tts=72000&EXPTABLE_exp_lvl145=7000000&tts=72000&EXPTABLE_exp_lvl146=7000000&tts=72000&EXPTABLE_exp_lvl147=7000000&tts=72000&EXPTABLE_exp_lvl148=7000000&tts=72000&EXPTABLE_exp_lvl149=7000000&tts=72000&EXPTABLE_exp_lvl150=7000000&tts=72000&EXPTABLE_exp_lvl151=7000000&tts=72000&EXPTABLE_exp_lvl152=7000000&tts=72000&EXPTABLE_exp_lvl153=7000000&tts=72000&EXPTABLE_exp_lvl154=7000000&tts=72000&EXPTABLE_exp_lvl155=7000000&tts=72000&EXPTABLE_exp_lvl156=7000000&tts=72000&EXPTABLE_exp_lvl157=7000000&tts=72000&EXPTABLE_exp_lvl158=7000000&tts=72000&EXPTABLE_exp_lvl159=7000000&tts=72000&EXPTABLE_exp_lvl160=7000000&tts=72000&EXPTABLE_exp_lvl161=7000000&tts=72000&EXPTABLE_exp_lvl162=7000000&tts=72000&EXPTABLE_exp_lvl163=7000000&tts=72000&EXPTABLE_exp_lvl164=7000000&tts=72000&EXPTABLE_exp_lvl165=7000000&tts=72000&EXPTABLE_exp_lvl166=7000000&tts=72000&EXPTABLE_exp_lvl167=7000000&tts=72000&EXPTABLE_exp_lvl168=7000000&tts=72000&EXPTABLE_exp_lvl169=7000000&tts=72000&EXPTABLE_exp_lvl170=7000000&tts=72000&EXPTABLE_exp_lvl171=7000000&tts=72000&EXPTABLE_exp_lvl172=7000000&tts=72000&EXPTABLE_exp_lvl173=7000000&tts=72000&EXPTABLE_exp_lvl174=7000000&tts=72000&EXPTABLE_exp_lvl175=7000000&tts=72000&EXPTABLE_exp_lvl176=7000000&tts=72000&EXPTABLE_exp_lvl177=7000000&tts=72000&EXPTABLE_exp_lvl178=7000000&tts=72000&EXPTABLE_exp_lvl179=7000000&tts=72000&EXPTABLE_exp_lvl180=7000000&tts=72000&EXPTABLE_exp_lvl181=7000000&tts=72000&EXPTABLE_exp_lvl182=7000000&tts=72000&EXPTABLE_exp_lvl183=7000000&tts=72000&EXPTABLE_exp_lvl184=7000000&tts=72000&EXPTABLE_exp_lvl185=7000000&tts=72000&EXPTABLE_exp_lvl186=7000000&tts=72000&EXPTABLE_exp_lvl187=7000000&tts=72000&EXPTABLE_exp_lvl188=7000000&tts=72000&EXPTABLE_exp_lvl189=7000000&tts=72000&EXPTABLE_exp_lvl190=7000000&tts=72000&EXPTABLE_exp_lvl191=7000000&tts=72000&EXPTABLE_exp_lvl192=7000000&tts=72000&EXPTABLE_exp_lvl193=7000000&tts=72000&EXPTABLE_exp_lvl194=7000000&tts=72000&EXPTABLE_exp_lvl195=7000000&tts=72000&EXPTABLE_exp_lvl196=7000000&tts=72000&EXPTABLE_exp_lvl197=7000000&tts=72000&EXPTABLE_exp_lvl198=7000000&tts=72000&EXPTABLE_exp_lvl199=7000000&tts=72000&EXPTABLE_exp_lvl200=7000000&tts=72000&EXPTABLE_exp_lvl201=15000000&tts=72000&EXPTABLE_exp_lvl202=15000000&tts=72000&EXPTABLE_exp_lvl203=15000000&tts=72000&EXPTABLE_exp_lvl204=15000000&tts=72000&EXPTABLE_exp_lvl205=15000000&tts=72000&EXPTABLE_exp_lvl206=15000000&tts=72000&EXPTABLE_exp_lvl207=15000000&tts=72000&EXPTABLE_exp_lvl208=15000000&tts=72000&EXPTABLE_exp_lvl209=15000000&tts=72000&EXPTABLE_exp_lvl210=15000000&tts=72000&EXPTABLE_exp_lvl211=15000000&tts=72000&EXPTABLE_exp_lvl212=15000000&tts=72000&EXPTABLE_exp_lvl213=15000000&tts=72000&EXPTABLE_exp_lvl214=15000000&tts=72000&EXPTABLE_exp_lvl215=15000000&tts=72000&EXPTABLE_exp_lvl216=15000000&tts=72000&EXPTABLE_exp_lvl217=15000000&tts=72000&EXPTABLE_exp_lvl218=15000000&tts=72000&EXPTABLE_exp_lvl219=15000000&tts=72000&EXPTABLE_exp_lvl220=15000000&tts=72000&EXPTABLE_exp_lvl221=15500000&tts=72000&EXPTABLE_exp_lvl222=16000000&tts=72000&EXPTABLE_exp_lvl223=16500000&tts=72000&EXPTABLE_exp_lvl224=17000000&tts=72000&EXPTABLE_exp_lvl225=17500000&tts=72000&EXPTABLE_exp_lvl226=18000000&tts=72000&EXPTABLE_exp_lvl227=18500000&tts=72000&EXPTABLE_exp_lvl228=19000000&tts=72000&EXPTABLE_exp_lvl229=19500000&tts=72000&EXPTABLE_exp_lvl230=20000000&tts=72000&EXPTABLE_exp_lvl231=20500000&tts=72000&EXPTABLE_exp_lvl232=21000000&tts=72000&EXPTABLE_exp_lvl233=21500000&tts=72000&EXPTABLE_exp_lvl234=22000000&tts=72000&EXPTABLE_exp_lvl235=22500000&tts=72000&EXPTABLE_exp_lvl236=23000000&tts=72000&EXPTABLE_exp_lvl237=23500000&tts=72000&EXPTABLE_exp_lvl238=24000000&tts=72000&EXPTABLE_exp_lvl239=24500000&tts=72000&EXPTABLE_exp_lvl240=25000000&tts=72000&EXPTABLE_exp_lvl241=25500000&tts=72000&EXPTABLE_exp_lvl242=26000000&tts=72000&EXPTABLE_exp_lvl243=26500000&tts=72000&EXPTABLE_exp_lvl244=27000000&tts=72000&EXPTABLE_exp_lvl245=27500000&tts=72000&EXPTABLE_exp_lvl246=28000000&tts=72000&EXPTABLE_exp_lvl247=28500000&tts=72000&EXPTABLE_exp_lvl248=29000000&tts=72000&EXPTABLE_exp_lvl249=29500000&tts=72000&EXPTABLE_exp_lvl250=30000000&tts=72000&EXPTABLE_exp_lvl251=30500000&tts=72000&EXPTABLE_exp_lvl252=31000000&tts=72000&EXPTABLE_exp_lvl253=31500000&tts=72000&EXPTABLE_exp_lvl254=32000000&tts=72000&EXPTABLE_exp_lvl255=32500000&tts=72000&EXPTABLE_exp_lvl256=33000000&tts=72000&EXPTABLE_exp_lvl257=33500000&tts=72000&EXPTABLE_exp_lvl258=34000000&tts=72000&EXPTABLE_exp_lvl259=34500000&tts=72000&EXPTABLE_exp_lvl260=35000000&tts=72000&EXPTABLE_exp_lvl261=35500000&tts=72000&EXPTABLE_exp_lvl262=36000000&tts=72000&EXPTABLE_exp_lvl263=36500000&tts=72000&EXPTABLE_exp_lvl264=37000000&tts=72000&EXPTABLE_exp_lvl265=37500000&tts=72000&EXPTABLE_exp_lvl266=38000000&tts=72000&EXPTABLE_exp_lvl267=38500000&tts=72000&EXPTABLE_exp_lvl268=39000000&tts=72000&EXPTABLE_exp_lvl269=39500000&tts=72000&EXPTABLE_exp_lvl270=40000000&tts=72000&EXPTABLE_exp_lvl271=40500000&tts=72000&EXPTABLE_exp_lvl272=41000000&tts=72000&EXPTABLE_exp_lvl273=41500000&tts=72000&EXPTABLE_exp_lvl274=42000000&tts=72000&EXPTABLE_exp_lvl275=42500000&tts=72000&EXPTABLE_exp_lvl276=43000000&tts=72000&EXPTABLE_exp_lvl277=43500000&tts=72000&EXPTABLE_exp_lvl278=44000000&tts=72000&EXPTABLE_exp_lvl279=44500000&tts=72000&EXPTABLE_exp_lvl280=45000000&tts=72000&EXPTABLE_exp_lvl281=45500000&tts=72000&EXPTABLE_exp_lvl282=46000000&tts=72000&EXPTABLE_exp_lvl283=46500000&tts=72000&EXPTABLE_exp_lvl284=47000000&tts=72000&EXPTABLE_exp_lvl285=47500000&tts=72000&EXPTABLE_exp_lvl286=48000000&tts=72000&EXPTABLE_exp_lvl287=48500000&tts=72000&EXPTABLE_exp_lvl288=49000000&tts=72000&EXPTABLE_exp_lvl289=49500000&tts=72000&EXPTABLE_exp_lvl290=50000000&tts=72000&EXPTABLE_exp_lvl291=50500000&tts=72000&EXPTABLE_exp_lvl292=51000000&tts=72000&EXPTABLE_exp_lvl293=51500000&tts=72000&EXPTABLE_exp_lvl294=52000000&tts=72000&EXPTABLE_exp_lvl295=52500000&tts=72000&EXPTABLE_exp_lvl296=53000000&tts=72000&EXPTABLE_exp_lvl297=53500000&tts=72000&EXPTABLE_exp_lvl298=54000000&tts=72000&EXPTABLE_exp_lvl299=54500000&tts=72000&EXPTABLE_exp_lvl300=55000000&tts=72000&EXPTABLE_exp_lvl301=55500000&tts=72000&EXPTABLE_exp_lvl302=56000000&tts=72000&EXPTABLE_exp_lvl303=56500000&tts=72000&EXPTABLE_exp_lvl304=57000000&tts=72000&EXPTABLE_exp_lvl305=57500000&tts=72000&EXPTABLE_exp_lvl306=58000000&tts=72000&EXPTABLE_exp_lvl307=58500000&tts=72000&EXPTABLE_exp_lvl308=59000000&tts=72000&EXPTABLE_exp_lvl309=59500000&tts=72000&EXPTABLE_exp_lvl310=60000000&tts=72000&EXPTABLE_exp_lvl311=60500000&tts=72000&EXPTABLE_exp_lvl312=61000000&tts=72000&EXPTABLE_exp_lvl313=61500000&tts=72000&EXPTABLE_exp_lvl314=62000000&tts=72000&EXPTABLE_exp_lvl315=62500000&tts=72000&EXPTABLE_exp_lvl316=63000000&tts=72000&EXPTABLE_exp_lvl317=63500000&tts=72000&EXPTABLE_exp_lvl318=64000000&tts=72000&EXPTABLE_exp_lvl319=64500000&tts=72000&EXPTABLE_exp_lvl320=65000000&tts=72000&EXPTABLE_exp_lvl321=65600000&tts=72000&EXPTABLE_exp_lvl322=66200000&tts=72000&EXPTABLE_exp_lvl323=66800000&tts=72000&EXPTABLE_exp_lvl324=67400000&tts=72000&EXPTABLE_exp_lvl325=68000000&tts=72000&EXPTABLE_exp_lvl326=69200000&tts=72000&EXPTABLE_exp_lvl327=70400000&tts=72000&EXPTABLE_exp_lvl328=71600000&tts=72000&EXPTABLE_exp_lvl329=72800000&tts=72000&EXPTABLE_exp_lvl330=74000000&tts=72000&EXPTABLE_exp_lvl331=75200000&tts=72000&EXPTABLE_exp_lvl332=76400000&tts=72000&EXPTABLE_exp_lvl333=77600000&tts=72000&EXPTABLE_exp_lvl334=78800000&tts=72000&EXPTABLE_exp_lvl335=80000000&tts=72000&EXPTABLE_exp_lvl336=81200000&tts=72000&EXPTABLE_exp_lvl337=82400000&tts=72000&EXPTABLE_exp_lvl338=83600000&tts=72000&EXPTABLE_exp_lvl339=84800000&tts=72000&EXPTABLE_exp_lvl340=86000000&tts=72000&EXPTABLE_exp_lvl341=87200000&tts=72000&EXPTABLE_exp_lvl342=88400000&tts=72000&EXPTABLE_exp_lvl343=89600000&tts=72000&EXPTABLE_exp_lvl344=90800000&tts=72000&EXPTABLE_exp_lvl345=92000000&tts=72000&EXPTABLE_exp_lvl346=93200000&tts=72000&EXPTABLE_exp_lvl347=94400000&tts=72000&EXPTABLE_exp_lvl348=95600000&tts=72000&EXPTABLE_exp_lvl349=96800000&tts=72000&EXPTABLE_exp_lvl350=98000000&tts=72000&EXPTABLE_exp_lvl351=99200000&tts=72000&EXPTABLE_exp_lvl352=100400000&tts=72000&EXPTABLE_exp_lvl353=101600000&tts=72000&EXPTABLE_exp_lvl354=102800000&tts=72000&EXPTABLE_exp_lvl355=104000000&tts=72000&EXPTABLE_exp_lvl356=105200000&tts=72000&EXPTABLE_exp_lvl357=106400000&tts=72000&EXPTABLE_exp_lvl358=107600000&tts=72000&EXPTABLE_exp_lvl359=108800000&tts=72000&EXPTABLE_exp_lvl360=110000000&tts=72000&EXPTABLE_exp_lvl361=111200000&tts=72000&EXPTABLE_exp_lvl362=112400000&tts=72000&EXPTABLE_exp_lvl363=113600000&tts=72000&EXPTABLE_exp_lvl364=114800000&tts=72000&EXPTABLE_exp_lvl365=116000000&tts=72000&EXPTABLE_exp_lvl366=117200000&tts=72000&EXPTABLE_exp_lvl367=118400000&tts=72000&EXPTABLE_exp_lvl368=119600000&tts=72000&EXPTABLE_exp_lvl369=120800000&tts=72000&EXPTABLE_exp_lvl370=122000000&tts=72000&EXPTABLE_exp_lvl371=123200000&tts=72000&EXPTABLE_exp_lvl372=124400000&tts=72000&EXPTABLE_exp_lvl373=125600000&tts=72000&EXPTABLE_exp_lvl374=126800000&tts=72000&EXPTABLE_exp_lvl375=128000000&tts=72000&EXPTABLE_exp_lvl376=129200000&tts=72000&EXPTABLE_exp_lvl377=130400000&tts=72000&EXPTABLE_exp_lvl378=131600000&tts=72000&EXPTABLE_exp_lvl379=132800000&tts=72000&EXPTABLE_exp_lvl380=134000000&tts=72000&EXPTABLE_exp_lvl381=135200000&tts=72000&EXPTABLE_exp_lvl382=136400000&tts=72000&EXPTABLE_exp_lvl383=137600000&tts=72000&EXPTABLE_exp_lvl384=138800000&tts=72000&EXPTABLE_exp_lvl385=140000000&tts=72000&EXPTABLE_exp_lvl386=141200000&tts=72000&EXPTABLE_exp_lvl387=142400000&tts=72000&EXPTABLE_exp_lvl388=143600000&tts=72000&EXPTABLE_exp_lvl389=144800000&tts=72000&EXPTABLE_exp_lvl390=146000000&tts=72000&EXPTABLE_exp_lvl391=147200000&tts=72000&EXPTABLE_exp_lvl392=148400000&tts=72000&EXPTABLE_exp_lvl393=149600000&tts=72000&EXPTABLE_exp_lvl394=150800000&tts=72000&EXPTABLE_exp_lvl395=152000000&tts=72000&EXPTABLE_exp_lvl396=153200000&tts=72000&EXPTABLE_exp_lvl397=154400000&tts=72000&EXPTABLE_exp_lvl398=155600000&tts=72000&EXPTABLE_exp_lvl399=156800000&tts=72000&EXPTABLE_exp_lvl400=158000000&tts=72000&EXPTABLE_exp_lvl401=159200000&tts=72000&EXPTABLE_exp_lvl402=160400000&tts=72000&EXPTABLE_exp_lvl403=161600000&tts=72000&EXPTABLE_exp_lvl404=162800000&tts=72000&EXPTABLE_exp_lvl405=164000000&tts=72000&EXPTABLE_exp_lvl406=165200000&tts=72000&EXPTABLE_exp_lvl407=166400000&tts=72000&EXPTABLE_exp_lvl408=167600000&tts=72000&EXPTABLE_exp_lvl409=168800000&tts=72000&EXPTABLE_exp_lvl410=170000000&tts=72000&EXPTABLE_exp_lvl411=171200000&tts=72000&EXPTABLE_exp_lvl412=172400000&tts=72000&EXPTABLE_exp_lvl413=173600000&tts=72000&EXPTABLE_exp_lvl414=174800000&tts=72000&EXPTABLE_exp_lvl415=176000000&tts=72000');
                                                                                </script>
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                    <td width="12" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/stats_right.jpg"></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td width="709" class="design2010" style="background-image: url('https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/innerbkg.jpg');
                             background-attachment: scroll;
                             background-position: top right;
                             background-repeat: no-repeat;">
                                                            <div align=center>
                                                                <a href="index.php?action=profile;sa=account">(Click here to setup a recovery email address for this account)</a>
                                                                <br>
                                                            </div>
                                </div>
                                <script language="Javascript" type="text/javascript">
                                    function toggle(targetId) {
                                        var state = 0;
                                        var blockname = "block" + targetId;
                                        var blockimage = "blockcollapse" + targetId;

                                        if (document.getElementById) {
                                            target = document.getElementById(blockname);
                                            if (target.style.display == "none") {
                                                target.style.display = "";
                                                state = 1;
                                            } else {
                                                target.style.display = "none";
                                                state = 0;
                                            }

                                            document.getElementById(blockimage).src = smf_images_url + (state ? "/collapse.gif" : "/expand.gif");
                                            var tempImage = new Image();
                                            tempImage.src = "https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=tpmod;upshrink=" + targetId + ";state=" + state + ";" + (new Date().getTime());

                                        }
                                    }
                                </script>
                                <table cellpadding="0" cellspacing="0" border="0" width="100%" style="table-layout: fixed;">
                                    <tr>
                                        <td valign="top">
                                            <div style="">
                                                <table width="100%" cellpadding="0" cellspacing="0">
                                                    <tr height="500">
                                                        <td valign="top" style="padding: 4px;">
                                                            <div style="_height: 1%; overflow: auto; ">
                                                                <div style="line-height: 1.3em; ">
                                                                    <script>
                                                                        function StartTime() {
                                                                            TimeOut = window.setTimeout("if(Get_Cookie('InInnerCity********')!=1) {window.location = 'index.php';}", 300000);
                                                                        }

                                                                        //window.onLoad=StartTime();

                                                                        function setcookie(cookieName, cookieValue, nDays) {
                                                                            var today = new Date();
                                                                            var expire = new Date();
                                                                            if (nDays == null || nDays == 0)
                                                                                nDays = 1;
                                                                            expire.setTime(today.getTime() + 3600000 * 24 * nDays);
                                                                            document.cookie = cookieName + "=" + escape(cookieValue) + ";expires=" + expire.toGMTString();
                                                                        }

                                                                        function Get_Cookie(check_name) {

                                                                            var a_all_cookies = document.cookie.split(";");
                                                                            var a_temp_cookie = "";
                                                                            var cookie_name = "";
                                                                            var cookie_value = "";
                                                                            var b_cookie_found = false;
                                                                            // set boolean t/f default f

                                                                            for (i = 0; i < a_all_cookies.length; i++) {

                                                                                a_temp_cookie = a_all_cookies[i].split("=");

                                                                                // and trim left/right whitespace while were at it
                                                                                cookie_name = a_temp_cookie[0].replace(/^\s+|\s+$/g, "");

                                                                                // if the extracted name matches passed check_name
                                                                                if (cookie_name == check_name) {
                                                                                    b_cookie_found = true;
                                                                                    // we need to handle case where cookie has no value but exists (no = sign, that is):
                                                                                    if (a_temp_cookie.length > 1) {
                                                                                        cookie_value = unescape(a_temp_cookie[1].replace(/^\s+|\s+$/g, ""));
                                                                                    }
                                                                                    // note that in cases where cookie is initialized but no value, null is returned
                                                                                    return cookie_value;
                                                                                    break;
                                                                                }
                                                                                a_temp_cookie = null;
                                                                                cookie_name = "";
                                                                            }
                                                                            if (!b_cookie_found) {
                                                                                return null;
                                                                            }
                                                                        }
                                                                    </script>
                                                                    <div id='htmlFlashReplace'>
                                                                        <link rel='stylesheet' href='hotrods/hotrods_v9_0_11/HTML5/css/outpost.css'>
                                                                        <script type='text/javascript' src='hotrods/hotrods_v9_0_11/HTML5/js/outpost.js'></script>
                                                                        <div class='notranslate' id='outpost'>
                                                                            <div id='textAddon'></div>
                                                                            <div id='prompt'>
                                                                                <div id='gamecontent'></div>
                                                                            </div>
                                                                        </div>
                                                                        <script type='text/javascript'>
                                                                            initData('freepoints=1&sc=ca30f254f9b412cad1c84fdd895fa866&userID=********&template_ID=0&password=02c5eb23839347bfed157e6277364004735bad2e&defenseon=0&lastarea=&minioutpost=1&minioutpostname=Secronom Bunker&df_tradezone=21&gameversion=1&df_cash=111&df_profession=Investigator&arenacount=1', initOutpost);
                                                                        </script>
                                                                    </div>
                                                                    <table width=550 align=center>
                                                                        <tr>
                                                                            <td align=left>
                                                                                <a href="#" onclick="window.open('https://deadfrontier.com/OA.php', 'popup', 'width=780,height=271');">OA Warning System</a>
                                                                            </td>
                                                                            <td align=right>
                                                                                <a id="DFHelpBox" class="iframe" href="tutorials.php?tutorial=outpost">Need Help?</a>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
</div><div id="ajax_in_progress" style="display: none;">Loading...</div>
</td></tr></table></td></tr></table></td>
<td width="39" class="design2010" style="background: url(https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/extend_right_edge.jpg) repeat-y;" valign="top">
    <table width="39" class="design2010" border="0" width="985" align="center" cellpadding="0" cellspacing="0">
        <tr height="597">
            <td width="39" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/right_edge.jpg"></td>
        </tr>
    </table>
</td>
</tr></table>
<table class="design2010" border="0" width="985" height="80" align="center" cellpadding="0" cellspacing="0">
    <tr height="80">
        <td width="59" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/footer_left.jpg"></td>
        <td width="859" class="design2010">
            <table class="design2010" border="0" width="859" align="center" cellpadding="0" cellspacing="0">
                <tr height="16">
                    <td width="859" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/footer_top.jpg"></td>
                </tr>
                <tr height="23">
                    <td width="859" class="design2010" align="center" valign="middle">
                        <!-- players online here -->
                        <table class="design2010" width=98%>
                            <tr>
                                <td class="design2010" align=left width=370>
                                    <font size=1 color=yellow>
                                        GM until 25th of March 2026 (<a href="index.php?page=90">?</a>
                                        )
                                    </font>
                                </td>
                                <td class="design2010" align=left>4612 Players Online</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr height="41">
                    <td width="859" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/footer_bottom.jpg"></td>
                </tr>
            </table>
        </td>
        <td width="69" class="design2010" background="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/footer_right.jpg"></td>
    </tr>
</table>
<!-- music and cb -->
<table class="design2010" border="0" height="60" align="center" cellpadding="0" cellspacing="0">
    <tr height="31"><td class="design2010"">
                      <table class="design2010 " border="0 " align="center " cellpadding="0 " cellspacing="0 ">
                        <tr height="31 ">
                         <td class="design2010 " width="371 "></td>
                         <td class="design2010 " width="21 " background = "https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/music_left.jpg "></td>


                         <td class="design2010 " width="89 "> <a href="https://discordapp.com/invite/deadfrontier2 " target="_blank "><img alt="Turn CB Radio On " name="chat " src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/chat.jpg " onmouseover="document['chat'].src='https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/chat_over.jpg';" onmouseout="document['chat'].src='https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/chat.jpg';"></a></td>
                         <td class="design2010 " width="98 "> <a href="#"><img alt="Turn Music On " onclick="window.open('https://www.deadfrontier.com/dfmusic.html','musicwindow','width=200,height=105')" name="music " src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/music.jpg " onmouseover="document['music'].src='https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/music_over.jpg';" onmouseout="document['music'].src='https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/music.jpg';"></a></td>
                         <td class="design2010 " width="31 " background = "https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/music_right.jpg "></td>
                         <td class="design2010 " width="375 "></td>
                        </tr>
                      </table>
                    </td>
                 </tr>
                 <tr height="29 ">
                    <td class="design2010 ">
                       <table class="design2010 " border="0 " align="center " cellpadding="0 " cellspacing="0 ">
                        <tr height="29 ">
                         <td class="design2010 " width="371 "></td>
                         <td class="design2010 " width="239 " background = "https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/music_bottom.jpg "></td>
                         <td class="design2010 " width="375 "></td>
                        </tr>
                      </table>
                    </td>
                </tr>
            </table>

          </td>

        </tr>

      </table>
    </td>

    <td class="design2010 " width ="50%" style="background-image:url('https://files.deadfrontier.com/deadfrontier/DF3Dimages/mainpage/right_margin.jpg');background-attachment:scroll;background-position:top left;background-repeat:no-repeat;">
    </td>
  </tr>
</table>


<!-- social media -->
<table class="design2010 " width="875 " align = "center " width="100%" border="0 " align="center " cellpadding="0 " cellspacing="0 ">
  <tr>
    <td width="300 " class="design2010 "></td>
    <td class="design2010 " width="275 " align="center " valign="bottom ">
      <font style="font-size:10px " color="#876745 ">Powered by SMF | Copyright&copy Simple Machines 2006</font>
    </td>
    <td width="300 " class="design2010 " align="right ">
      <a href="https://www.youtube.com/user/DeadFrontierMMO/"><img src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/youtube.jpg "></a>
      <a href="https://deadfrontierdev.blogspot.com/"><img src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/blogger.jpg "></a>
      <a href="https://www.twitter.com/DeadFrontierMMO/"><img src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/twitter.jpg "></a>
      <a href="https://www.facebook.com/pages/Dead-Frontier-MMO/218205323311 "><img src="https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/facebook.jpg "></a>
    </td>
  </tr>
</table>

<!-- footer -->
<table class="design2010 " align = "center " border="0 " align="center " cellpadding="0 " cellspacing="0 ">
  <tr height="139 ">
    <td class="design2010 " width="130 " background = "https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/footer_left.jpg "></td>
    <td class="design2010 " width="676 ">
<table class="design2010 " width = "676 " border="0 " align="center " cellpadding="0 " cellspacing="0 ">
          <tr height="49 ">
            <td class="design2010 " width = "676 " background = "https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/footer_top.jpg "></td>
          </tr>
          <tr height="31 ">
            <td class="design2010 " width = "676 " background = "https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/footer_middle.jpg ">
                <!-- footer text & links goes here -->
                 <!-- footer text & links goes here -->
                <table class="design2010 " width="676 "><tr><td class="design2010 " align="left "><font style="font-size:10px;"><font style="font-size:10px " color="#876745 ">Copyright&copy <a href="https://www.creakycorpse.com " target="_blank "><font color="#981313 ">Creaky Corpse Ltd</font></a> <script type="text/javascript ">var year = new Date();document.write(year.getFullYear());</script>  |  <a href="https://files.deadfrontier.com/deadfrontier/frontpage/DeadFrontierPRMaterials.zip "><font color="#981313 ">Press Info & Materials</font></a>  |  <a href="#" onclick="window.open('https://www.deadfrontier.com/privacy.html','privacy','width=1000,height=750, scrollbars=yes')"><font color="#981313 ">Privacy Policy</font></a>   |  <a href="#" onclick="window.open('https://www.deadfrontier.com/termsofservice.html','tos','width=1000,height=750, scrollbars=yes')"><font color="#981313 ">TOS</font></a>   |  <a href="#" onclick="window.open('https://www.deadfrontier.com/rules.html','rules','width=1000,height=750, scrollbars=yes')"><font color="#981313 ">Rules</font></a>   |  <a href="#" onclick="window.open('https://www.deadfrontier.com/credits.html','credits','width=600,height=500, scrollbars=yes')"><font color="#981313 ">Credits</font></a></font></font></td><td class="design2010 " align="right "><font style="font-size:10px;"><font style="font-size:10px " color="#876745 ">Site Design by <a href="https://www.fullyillustrated.com " target="_blank "><font color="#981313 ">Fully Illustrated</font></a></font></font></td></tr></table>
             </td>
          </tr>
          <tr height="59 ">
            <td class="design2010 " width = "676 " background = "https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/footer_bottom.jpg " align="center ">
            <font style="font-size:10px " color="#876745 ">Creaky Corpse Ltd, 3rd Floor 207 Regent Street, London, W1B 3HH, UK</font>
            </td>
          </tr>
        </table>
    </td>
    <td class="design2010 " width="65 " background = "https://files.deadfrontier.com/deadfrontier/DF3Dimages/frontpage/footer_right.jpg "></td>
  </tr>
</table>








</body>
</html>


