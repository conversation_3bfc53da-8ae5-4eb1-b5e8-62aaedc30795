var tradeTimer = 30000;
var requestTrack = 0;
var tradeTimeout;
var inTradeWindow = false;

function initiatePrivateItemForItem() {
  df_prompt.innerHTML =
    "<div style='text-align: center'>Loading, please wait...</div>";
  df_prompt.parentNode.style.display = "block";
  inPrivateTrading = true;
  marketScreen = "itemforitem";
  pageLogo = document.getElementById("pageLogo");
  pageLogo.dataset.marketType = "";
  initiateItemForItem();
}

function initiateItemForItem() {
  marketHolder = document.getElementById("marketplace");

  if (inPrivateTrading) {
    var dataArr = {};

    dataArr["action"] = "gettrade_b";
    dataArr["userID"] = userVars["userID"];
    dataArr["memberto"] = userVars["member_to"];
    dataArr["password"] = userVars["password"];
    dataArr["sc"] = userVars["sc"];
    var callURL = "private_trade";
    webCall(callURL, dataArr, populatePrivateItemForItem, true);
  } else {
    loadItemForItem();
  }
}

function loadItemForItem() {
  inTradeWindow = false;
  promptLoading();
  pageLogo.textContent = "Item-for-Item";
  pageLogo.dataset.marketType = "";

  var collectionBoxButtonHolder = document.createElement("div");
  collectionBoxButtonHolder.classList.add("opElem");
  collectionBoxButtonHolder.style.left = "0";
  collectionBoxButtonHolder.style.right = "0";
  collectionBoxButtonHolder.style.top = "76px";

  var collectionBoxButton = document.createElement("button");
  collectionBoxButton.textContent = "Collect Trade Items";
  collectionBoxButton.style.marginRight = "5px";
  collectionBoxButton.onclick = displayCollectionBox;

  collectionBoxButtonHolder.appendChild(collectionBoxButton);

  var archiveButton = document.createElement("button");
  archiveButton.textContent = "Archived Trades";
  archiveButton.style.marginLeft = "5px";
  archiveButton.onclick = defaultDisplayArchive;

  collectionBoxButtonHolder.appendChild(archiveButton);

  marketHolder.appendChild(collectionBoxButtonHolder);

  var tradeLabels = document.createElement("div");
  tradeLabels.classList.add("tradeLabels");

  tradeLabels.innerHTML = "<span>Trader</span>";
  tradeLabels.innerHTML += "<span style='left: 154px;'>Items</span>";
  tradeLabels.innerHTML +=
    "<span class='cashhack' data-cash='Cash' style='left: 268px;'>Cash</span>";
  tradeLabels.innerHTML +=
    "<span class='cashhack credits' data-cash='Credits' style='left: 384px;'>Credits</span>";

  marketHolder.appendChild(tradeLabels);

  var dataArr = {};

  dataArr["action"] = "gettrades";
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];
  dataArr["sc"] = userVars["sc"];

  var tradeDisplay = document.createElement("div");
  tradeDisplay.id = "itemDisplay";
  tradeDisplay.classList.add("marketDataHolder");

  tradeDisplay.innerHTML = "";

  var callURL = "private_trade";
  webCall(
    callURL,
    dataArr,
    function (data) {
      data = flshToArr(data);

      if (data["tradelist_totalsales"]) {
        tradeListSize = data["tradelist_totalsales"];
      }
      var tradeSlots = document.createElement("div");
      tradeSlots.classList.add("tradeSlotDisplay");
      tradeSlots.style.bottom = "90px";
      tradeSlots.style.right = "25px";
      tradeSlots.textContent =
        tradeListSize + " / " + userVars["DFSTATS_df_invslots"];

      if (checkLSBool("general", "statusPercents")) {
        tradeSlots.textContent +=
          " (" +
          Math.round(
            (parseInt(tradeListSize) /
              parseInt(userVars["DFSTATS_df_invslots"])) *
              100
          ) +
          "%)";
      }

      marketHolder.appendChild(tradeSlots);

      for (var i = 0; i < data["trades_maxresults"]; i++) {
        var tradeID = "trade" + i;
        var tradeSlot = document.createElement("div");
        tradeSlot.classList.add("tradeSlot");

        var yourPlayer = "";
        var theirPlayer = "";

        if (data[tradeID + "_player1_id"] === userVars["userID"]) {
          yourPlayer = "player1";
          theirPlayer = "player2";
        } else {
          yourPlayer = "player2";
          theirPlayer = "player1";
        }

        var declineButton = document.createElement("button");
        declineButton.textContent = "decline";
        declineButton.dataset.action = "declineTrade";
        declineButton.style.right = "8px";

        var viewTradeButton = document.createElement("button");
        viewTradeButton.textContent = "view trade";
        viewTradeButton.dataset.name =
          data[tradeID + "_" + theirPlayer + "_account_name"];
        viewTradeButton.dataset.trade = data[tradeID + "_trade_id"];
        viewTradeButton.style.right = "64px";
        tradeSlot.innerHTML =
          data[tradeID + "_" + theirPlayer + "_account_name"] +
          "<span style='left: 160px;'>" +
          data[tradeID + "_total_items"] +
          "</span>";

        var total_cash = nf.format(
          parseInt(data[tradeID + "_player1_cash"]) +
            parseInt(data[tradeID + "_player2_cash"])
        );
        tradeSlot.innerHTML +=
          "<span class='cashhack' style='left: 274px;' data-cash='$" +
          total_cash +
          "'>$" +
          total_cash +
          "</span>";

        var total_credits = nf.format(
          parseInt(data[tradeID + "_player1_credits"]) +
            parseInt(data[tradeID + "_player2_credits"])
        );
        tradeSlot.innerHTML +=
          "<span class='cashhack credits' style='left: 390px;' data-cash='" +
          total_credits +
          "'>" +
          total_credits +
          "</span>";

        if (data[tradeID + "_player1_id"] === userVars["userID"]) {
          viewTradeButton.dataset.target = data[tradeID + "_player2_id"];
          declineButton.dataset.target = data[tradeID + "_player2_id"];
          if (
            data[tradeID + "_player2_id"] === "3" ||
            data[tradeID + "_player2_id"] === "42"
          ) {
            declineButton.disabled = true;
          }
        } else {
          viewTradeButton.dataset.target = data[tradeID + "_player1_id"];
          declineButton.dataset.target = data[tradeID + "_player1_id"];
          if (
            data[tradeID + "_player1_id"] === "3" ||
            data[tradeID + "_player1_id"] === "42"
          ) {
            declineButton.disabled = true;
          }
        }

        declineButton.dataset.update = data[tradeID + "_update_id"];
        declineButton.dataset.cash =
          parseInt(data[tradeID + "_player1_cash"]) +
          parseInt(data[tradeID + "_player2_cash"]);
        declineButton.dataset.credits =
          parseInt(data[tradeID + "_player1_credits"]) +
          parseInt(data[tradeID + "_player1_credits"]);
        declineButton.dataset.trade = data[tradeID + "_trade_id"];

        declineButton.onclick = i4iAction;

        viewTradeButton.onclick = function (e) {
          promptLoading("Fetching trade...");
          userVars["member_to_name"] = e.currentTarget.dataset.name;
          loadPrivateItemForItem(
            e.currentTarget.dataset.trade,
            e.currentTarget.dataset.target
          );
        };

        tradeSlot.appendChild(viewTradeButton);
        tradeSlot.appendChild(declineButton);

        tradeDisplay.appendChild(tradeSlot);
      }

      marketHolder.appendChild(tradeDisplay);
      promptEnd();
    },
    true
  );
}

function defaultDisplayArchive() {
  displayArchive(0);
}

function displayArchive(page) {
  inTradeWindow = false;
  marketHolder.innerHTML = "";
  promptLoading();
  pageLogo.textContent = "Item-for-Item - Archive";
  pageLogo.dataset.marketType = "";

  var archiveTextHelp = document.createElement("div");
  archiveTextHelp.classList.add("opElem");
  archiveTextHelp.style.top = "32px";
  archiveTextHelp.style.left = "120px";
  archiveTextHelp.style.right = "120px";
  archiveTextHelp.textContent =
    "Archived trades are ordered from most recent to oldest.";
  marketHolder.appendChild(archiveTextHelp);

  var tradeLabels = document.createElement("div");
  tradeLabels.classList.add("tradeLabels");

  tradeLabels.innerHTML = "<span>Trader</span>";
  tradeLabels.innerHTML += "<span style='left: 154px;'>Items</span>";
  tradeLabels.innerHTML +=
    "<span class='cashhack' data-cash='Cash' style='left: 268px;'>Cash</span>";
  tradeLabels.innerHTML +=
    "<span class='cashhack credits' data-cash='Credits' style='left: 384px;'>Credits</span>";

  marketHolder.appendChild(tradeLabels);

  var backButton = document.createElement("button");
  backButton.classList.add("opElem");
  backButton.textContent = "< TRADE PANEL";
  backButton.style.left = "12px";
  backButton.style.top = "8px";
  backButton.onclick = loadMarket;
  marketHolder.appendChild(backButton);

  var nextPageButton = document.createElement("button");
  nextPageButton.classList.add("opElem");
  nextPageButton.textContent = "Next Page >";
  nextPageButton.style.right = "12px";
  nextPageButton.style.top = "78px";
  nextPageButton.onclick = function () {
    displayArchive(page + 1);
  };
  marketHolder.appendChild(nextPageButton);

  var lastPageButton = document.createElement("button");
  lastPageButton.classList.add("opElem");
  lastPageButton.textContent = "< Last Page";
  lastPageButton.style.left = "12px";
  lastPageButton.style.top = "78px";
  lastPageButton.onclick = function () {
    displayArchive(page - 1);
  };
  marketHolder.appendChild(lastPageButton);
  if (page <= 0) {
    lastPageButton.disabled = true;
  }

  var dataArr = {};

  dataArr["action"] = "archive";
  dataArr["userID"] = userVars["userID"];
  dataArr["price"] = page;
  dataArr["password"] = userVars["password"];
  dataArr["sc"] = userVars["sc"];

  var tradeDisplay = document.createElement("div");
  tradeDisplay.id = "itemDisplay";
  tradeDisplay.classList.add("marketDataHolder");

  tradeDisplay.innerHTML = "";

  var callURL = "private_trade";
  webCall(
    callURL,
    dataArr,
    function (data) {
      data = flshToArr(data);

      if (parseInt(data["trades_maxresults"]) < 20) {
        nextPageButton.disabled = true;
      }

      if (data["tradelist_totalsales"]) {
        tradeListSize = data["tradelist_totalsales"];
      }
      var tradeSlots = document.createElement("div");
      tradeSlots.classList.add("tradeSlotDisplay");
      tradeSlots.style.bottom = "90px";
      tradeSlots.style.right = "25px";
      tradeSlots.textContent =
        tradeListSize + " / " + userVars["DFSTATS_df_invslots"];

      if (checkLSBool("general", "statusPercents")) {
        tradeSlots.textContent +=
          " (" +
          Math.round(
            (parseInt(tradeListSize) /
              parseInt(userVars["DFSTATS_df_invslots"])) *
              100
          ) +
          "%)";
      }

      marketHolder.appendChild(tradeSlots);

      for (var i = 0; i < data["trades_maxresults"]; i++) {
        var tradeID = "trade" + i;
        var tradeSlot = document.createElement("div");
        tradeSlot.classList.add("tradeSlot");

        var yourPlayer = "";
        var theirPlayer = "";

        if (data[tradeID + "_player1_id"] === userVars["userID"]) {
          yourPlayer = "player1";
          theirPlayer = "player2";
        } else {
          yourPlayer = "player2";
          theirPlayer = "player1";
        }

        var viewTradeButton = document.createElement("button");
        viewTradeButton.textContent = "view trade";
        viewTradeButton.dataset.name =
          data[tradeID + "_" + theirPlayer + "_account_name"];
        viewTradeButton.dataset.trade = data[tradeID + "_trade_id"];
        viewTradeButton.style.right = "8px";

        tradeSlot.innerHTML =
          data[tradeID + "_" + theirPlayer + "_account_name"] +
          "<span style='left: 160px;'>" +
          data[tradeID + "_total_items"] +
          "</span>";

        var total_cash = nf.format(
          parseInt(data[tradeID + "_player1_cash"]) +
            parseInt(data[tradeID + "_player2_cash"])
        );
        tradeSlot.innerHTML +=
          "<span class='cashhack' style='left: 274px;' data-cash='$" +
          total_cash +
          "'>$" +
          total_cash +
          "</span>";

        var total_credits = nf.format(
          parseInt(data[tradeID + "_player1_credits"]) +
            parseInt(data[tradeID + "_player2_credits"])
        );
        tradeSlot.innerHTML +=
          "<span class='cashhack credits' style='left: 390px;' data-cash='" +
          total_credits +
          "'>" +
          total_credits +
          "</span>";

        if (data[tradeID + "_player1_id"] === userVars["userID"]) {
          viewTradeButton.dataset.target = data[tradeID + "_player2_id"];
        } else {
          viewTradeButton.dataset.target = data[tradeID + "_player1_id"];
        }

        viewTradeButton.onclick = function (e) {
          promptLoading("Fetching trade...");
          userVars["member_to_name"] = e.currentTarget.dataset.name;
          loadPrivateItemForItem(
            e.currentTarget.dataset.trade,
            e.currentTarget.dataset.target
          );
        };

        tradeSlot.appendChild(viewTradeButton);

        tradeDisplay.appendChild(tradeSlot);
      }

      marketHolder.appendChild(tradeDisplay);
      promptEnd();
    },
    true
  );
}

function displayCollectionBox() {
  inTradeWindow = false;
  promptLoading("Loading your items...");
  pageLogo.textContent = "Collection Box";
  marketHolder.innerHTML = "";

  var backButton = document.createElement("button");
  backButton.classList.add("opElem");
  backButton.style.left = "12px";
  backButton.style.top = "8px";

  if (inPrivateTrading) {
    backButton.textContent = "< PRIVATE TRADE";
    backButton.onclick = initiatePrivateItemForItem;
  } else {
    backButton.textContent = "< TRADE PANEL";
    backButton.onclick = loadMarket;
  }

  marketHolder.appendChild(backButton);

  var dataArr = {};

  dataArr["action"] = "getitembox";
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];
  dataArr["sc"] = userVars["sc"];

  var tradeDisplay = document.createElement("div");
  tradeDisplay.id = "itemDisplay";
  tradeDisplay.classList.add("marketDataHolder");
  tradeDisplay.classList.add("collectionBox");

  tradeDisplay.innerHTML = "";

  var tradeSlots = document.createElement("div");
  tradeSlots.classList.add("tradeSlotDisplay");
  tradeSlots.style.bottom = "90px";
  tradeSlots.style.right = "25px";
  tradeSlots.textContent =
    tradeListSize + " / " + userVars["DFSTATS_df_invslots"];

  if (checkLSBool("general", "statusPercents")) {
    tradeSlots.textContent +=
      " (" +
      Math.round(
        (parseInt(tradeListSize) / parseInt(userVars["DFSTATS_df_invslots"])) *
          100
      ) +
      "%)";
  }

  marketHolder.appendChild(tradeSlots);

  var callURL = "private_trade";
  webCall(callURL, dataArr, function (data) {
    data = flshToArr(data);

    if (data["tradelist_totalsales"]) {
      tradeListSize = data["tradelist_totalsales"];
    }

    var freeSlot = findFirstEmptyGenericSlot("inv");

    for (var i = 0; i < data["total_items"]; i++) {
      var itemID = "item" + i;
      var itemSlot = document.createElement("div");

      var itemClass = data[itemID + "_item"].split("_")[0];

      itemSlot.classList.add("fakeItem");
      itemSlot.dataset.type = data[itemID + "_item"];
      itemSlot.dataset.quantity = data[itemID + "_quantity"];
      itemSlot.dataset.trade = data[itemID + "_trade_id"];

      var itemName = itemNamer(
        data[itemID + "_item"],
        data[itemID + "_quantity"]
      );
      var itemText =
        "<div class='itemName cashhack credits' data-cash='" +
        itemName +
        "'>" +
        itemName +
        "</div>";

      if (
        data[itemID + "_quantity"] &&
        parseInt(data[itemID + "_quantity"]) > 1 &&
        globalData[itemClass]["itemcat"] !== "armour"
      ) {
        itemText += " (" + data[itemID + "_quantity"] + ")";
      }

      itemSlot.innerHTML =
        itemText + calcMCTag(data[itemID + "_item"], false, "span", "");

      var collectButton = document.createElement("button");
      collectButton.textContent = "collect";
      collectButton.style.right = "8px";
      if (freeSlot !== false) {
        collectButton.dataset.action = "collectItem";
        collectButton.dataset.target = data[itemID + "_player_id"];
        collectButton.dataset.credits = data[itemID + "_time_added"];
        collectButton.onclick = i4iAction;
      } else {
        collectButton.disabled = true;
      }

      var viewTradeButton = document.createElement("button");
      viewTradeButton.textContent = "view trade";
      viewTradeButton.onclick = loadPrivateTrade;
      viewTradeButton.style.right = "64px";

      itemSlot.appendChild(collectButton);
      itemSlot.appendChild(viewTradeButton);

      tradeDisplay.appendChild(itemSlot);
    }
    marketHolder.appendChild(tradeDisplay);

    var collectAllButton = document.createElement("button");
    collectAllButton.textContent = "Collect All Items";
    collectAllButton.classList.add("opElem");
    collectAllButton.style.top = "100px";
    collectAllButton.style.right = "30px";

    if (freeSlot !== false && data["total_items"] > 0) {
      collectAllButton.dataset.action = "collectAllItems";
      collectAllButton.dataset.target = data[itemID + "_player_id"];
      collectAllButton.onclick = i4iAction;
    } else {
      collectAllButton.disabled = true;
    }

    marketHolder.appendChild(collectAllButton);

    promptEnd();
  });
}

function loadPrivateTrade(e) {
  promptLoading("Fetching trade...");

  var dataArr = {};

  dataArr["action"] = "gettrade_a";
  dataArr["userID"] = userVars["userID"];
  dataArr["trade"] = e.currentTarget.parentNode.dataset.trade;
  dataArr["memberto"] = userVars["member_to"];
  dataArr["password"] = userVars["password"];
  dataArr["sc"] = userVars["sc"];

  var callURL = "private_trade";
  webCall(callURL, dataArr, populatePrivateItemForItem, true);
}

function loadPrivateItemForItem(tradeID, targetID) {
  var dataArr = {};

  dataArr["action"] = "gettrade";
  dataArr["userID"] = userVars["userID"];
  dataArr["memberto"] = targetID;
  dataArr["trade"] = tradeID;
  dataArr["password"] = userVars["password"];
  dataArr["sc"] = userVars["sc"];

  var callURL = "private_trade";
  webCall(callURL, dataArr, populatePrivateItemForItem, true);
}

function populatePrivateItemForItem(data) {
  marketHolder.innerHTML = "";
  data = flshToArr(data);

  if (data["DFSTATS_df_credits"]) {
    var credits = "Credits: " + nf.format(userVars["DFSTATS_df_credits"]);
    for (let heldCredits of document.getElementsByClassName("heldCredits")) {
      heldCredits.textContent = cash;
      heldCredits.dataset.cash = cash;
    }
  }
  if (data["DFSTATS_df_cash"]) {
    var cash = "Cash: $" + nf.format(userVars["DFSTATS_df_cash"]);
    for (let heldCash of document.getElementsByClassName("heldCash")) {
      heldCash.textContent = cash;
      heldCash.dataset.cash = cash;
    }
  }
  if (data["tradelist_totalsales"]) {
    tradeListSize = data["tradelist_totalsales"];
  }

  var tradeSlots = document.createElement("div");
  tradeSlots.classList.add("tradeSlotDisplay");
  tradeSlots.style.bottom = "90px";
  tradeSlots.style.right = "25px";
  tradeSlots.textContent =
    tradeListSize + " / " + userVars["DFSTATS_df_invslots"];

  if (checkLSBool("general", "statusPercents")) {
    tradeSlots.textContent +=
      " (" +
      Math.round(
        (parseInt(tradeListSize) / parseInt(userVars["DFSTATS_df_invslots"])) *
          100
      ) +
      "%)";
  }

  marketHolder.appendChild(tradeSlots);

  var yourPlayer = "";
  var theirPlayer = "";

  if (data["player1_id"] === userVars["userID"]) {
    yourPlayer = "player1";
    theirPlayer = "player2";
  } else {
    yourPlayer = "player2";
    theirPlayer = "player1";
  }

  if (
    data[theirPlayer + "_account_name"] &&
    data[theirPlayer + "_account_name"] !== ""
  ) {
    userVars["member_to_name"] = data[theirPlayer + "_account_name"];
  }
  pageLogo.textContent = "Private Trading - " + userVars["member_to_name"];

  var yourHolder = document.createElement("div");
  yourHolder.classList.add("marketDataHolder");
  yourHolder.classList.add("itemHolder");
  yourHolder.classList.add("yourItems");
  yourHolder.classList.add("fakeSlot");
  if (
    (data["player1_accepted"] === "0" || data["player2_accepted"] === "0") &&
    data["declined"] !== "1" &&
    data[theirPlayer + "_id"] !== "3" &&
    data[theirPlayer + "_id"] !== "42"
  ) {
    inTradeWindow = true;
    yourHolder.dataset.action = "tradeitem";
  }
  yourHolder.dataset.target = data[theirPlayer + "_id"];
  yourHolder.dataset.credits =
    parseInt(data[yourPlayer + "_credits"]) +
    parseInt(data[theirPlayer + "_credits"]);
  yourHolder.dataset.trade = data["trade_id"];
  yourHolder.dataset.update = data["update_id"];

  var theirHolder = document.createElement("div");
  theirHolder.classList.add("marketDataHolder");
  theirHolder.classList.add("itemHolder");
  theirHolder.classList.add("theirItems");

  for (var i = 0; i < data["total_items"]; i++) {
    var itemID = "item" + i;
    var itemType = data[itemID + "_item"].split("_");
    var itemSlot = document.createElement("div");

    itemSlot.classList.add("fakeItem");
    itemSlot.dataset.type = data[itemID + "_item"];
    itemSlot.dataset.quantity = data[itemID + "_quantity"];

    var itemName = itemNamer(
      data[itemID + "_item"],
      data[itemID + "_quantity"]
    );
    var itemText =
      "<div class='itemName cashhack credits' data-cash='" +
      itemName +
      "'>" +
      itemName +
      "</div>";

    if (
      data[itemID + "_quantity"] &&
      parseInt(data[itemID + "_quantity"]) > 1 &&
      globalData[itemType[0]]["itemcat"] !== "armour"
    ) {
      itemText += " (" + data[itemID + "_quantity"] + ")";
    }

    itemSlot.innerHTML =
      itemText + calcMCTag(data[itemID + "_item"], false, "span", "");

    if (data[itemID + "_player_id"] === userVars["userID"]) {
      if (
        data["player1_accepted"] === "0" ||
        data["player2_accepted"] === "0"
      ) {
        var removeItemButton = document.createElement("button");
        removeItemButton.textContent = "remove";

        removeItemButton.style.right = "8px";

        removeItemButton.dataset.action = "removeItem";
        removeItemButton.dataset.target = data[theirPlayer + "_id"];
        removeItemButton.dataset.update = data["update_id"];
        removeItemButton.dataset.cash =
          parseInt(data[yourPlayer + "_cash"]) +
          parseInt(data[theirPlayer + "_cash"]);
        removeItemButton.dataset.credits =
          parseInt(data[yourPlayer + "_credits"]) +
          parseInt(data[theirPlayer + "_credits"]);
        removeItemButton.dataset.trade = data["trade_id"];

        removeItemButton.onclick = i4iAction;
        itemSlot.appendChild(removeItemButton);
      }

      yourHolder.appendChild(itemSlot);
    } else {
      theirHolder.appendChild(itemSlot);
    }
  }

  var yourLabel = document.createElement("div");
  yourLabel.classList.add("ifiLabel");
  yourLabel.style.left = 0;
  yourLabel.textContent = "Your Items";

  var theirLabel = document.createElement("div");
  theirLabel.classList.add("ifiLabel");

  theirLabel.style.left = "356px";
  if (userVars["member_to_name"].slice(-1) === "s") {
    theirLabel.innerHTML =
      "<a href='/onlinezombiemmo/index.php?action=profile;u=" +
      data[theirPlayer + "_id"] +
      "'>" +
      userVars["member_to_name"] +
      "'</a> Items";
  } else {
    theirLabel.innerHTML =
      "<a href='/onlinezombiemmo/index.php?action=profile;u=" +
      data[theirPlayer + "_id"] +
      "'>" +
      userVars["member_to_name"] +
      "'s</a> Items";
  }

  var yourCash = document.createElement("div");
  yourCash.classList.add("cashLabels");
  yourCash.classList.add("cashhack");
  yourCash.style.left = 0;
  yourCash.dataset.cash = "Cash: $";
  yourCash.textContent = "Cash: $";

  var yourCashInput = document.createElement("input");
  yourCashInput.classList.add("cashLabels");
  yourCashInput.style.left = "60px";
  yourCashInput.style.width = "65px";
  yourCashInput.type = "number";
  if (parseInt(userVars["DFSTATS_df_cash"]) >= 9999999999) {
    yourCashInput.max = 9999999999;
  } else {
    yourCashInput.max =
      parseInt(userVars["DFSTATS_df_cash"]) +
      parseInt(data[yourPlayer + "_cash"]);
  }
  yourCashInput.value = data[yourPlayer + "_cash"];

  yourCashInput.dataset.target = data[theirPlayer + "_id"];
  yourCashInput.dataset.credits =
    parseInt(data[yourPlayer + "_credits"]) +
    parseInt(data[theirPlayer + "_credits"]);
  yourCashInput.dataset.update = data["update_id"];
  yourCashInput.dataset.trade = data["trade_id"];
  yourCashInput.dataset.currency = "cash";
  yourCashInput.dataset.action = "changeCurrency";
  yourCashInput.onchange = i4iAction;

  yourCashInput.oninput = function (dE) {
    if (dE.target.value < 0) {
      dE.target.value = 0;
    } else if (parseInt(dE.target.value) > parseInt(dE.target.max)) {
      dE.target.value = dE.target.max;
    } else {
      dE.target.value = parseInt(dE.target.value);
      if (dE.target.value === "") {
        dE.target.value = 0;
      }
    }
  };

  if (
    (data["player1_accepted"] === "1" && data["player2_accepted"] === "1") ||
    data["declined"] === "1" ||
    data[theirPlayer + "_id"] === "3" ||
    data[theirPlayer + "_id"] === "42"
  ) {
    yourCashInput.disabled = true;
  }

  var theirCash = document.createElement("div");
  theirCash.classList.add("cashLabels");
  theirCash.classList.add("cashhack");

  theirCash.style.left = "356px";
  theirCash.dataset.cash = "Cash: $" + nf.format(data[theirPlayer + "_cash"]);
  theirCash.textContent = "Cash: $" + nf.format(data[theirPlayer + "_cash"]);

  var yourCredits = document.createElement("div");
  yourCredits.classList.add("cashLabels");
  yourCredits.classList.add("cashhack");
  yourCredits.classList.add("credits");
  yourCredits.style.left = "198px";
  yourCredits.dataset.cash = "Credits: ";
  yourCredits.textContent = "Credits: ";

  var yourCreditsInput = document.createElement("input");
  yourCreditsInput.classList.add("cashLabels");
  yourCreditsInput.style.left = "264px";
  yourCreditsInput.style.width = "65px";
  yourCreditsInput.type = "number";
  if (userVars["DFSTATS_df_credits"] >= 999999999) {
    yourCreditsInput.max = 999999999;
  } else {
    yourCreditsInput.max =
      parseInt(userVars["DFSTATS_df_credits"]) +
      parseInt(data[yourPlayer + "_credits"]);
  }
  yourCreditsInput.value = data[yourPlayer + "_credits"];

  yourCreditsInput.dataset.target = data[theirPlayer + "_id"];
  yourCreditsInput.dataset.credits =
    parseInt(data[yourPlayer + "_credits"]) +
    parseInt(data[theirPlayer + "_credits"]);
  yourCreditsInput.dataset.update = data["update_id"];
  yourCreditsInput.dataset.trade = data["trade_id"];
  yourCreditsInput.dataset.currency = "credit";
  yourCreditsInput.dataset.action = "changeCurrency";
  yourCreditsInput.onchange = i4iAction;

  yourCreditsInput.oninput = function (dE) {
    if (dE.target.value < 0) {
      dE.target.value = 0;
    } else if (parseInt(dE.target.value) > parseInt(dE.target.max)) {
      dE.target.value = dE.target.max;
    } else {
      dE.target.value = parseInt(dE.target.value);
      if (dE.target.value === "") {
        dE.target.value = 0;
      }
    }
  };

  if (
    (data["player1_accepted"] === "1" && data["player2_accepted"] === "1") ||
    data["declined"] === "1" ||
    data[theirPlayer + "_id"] === "3" ||
    data[theirPlayer + "_id"] === "42"
  ) {
    yourCreditsInput.disabled = true;
  }

  var theirCredits = document.createElement("div");
  theirCredits.classList.add("cashLabels");
  theirCredits.classList.add("cashhack");
  theirCredits.classList.add("credits");
  theirCredits.style.right = 0;
  theirCredits.dataset.cash =
    "Credits: " + nf.format(data[theirPlayer + "_credits"]);
  theirCredits.textContent =
    "Credits: " + nf.format(data[theirPlayer + "_credits"]);

  var yourAccept = document.createElement("div");
  yourAccept.classList.add("ifiLabel");
  yourAccept.classList.add("cashhack");
  yourAccept.style.right = "356px";
  if (data[yourPlayer + "_accepted"] === "0") {
    yourAccept.classList.add("redElements");
    yourAccept.dataset.cash = "Not Accepted";
    yourAccept.textContent = "Not Accepted";
  } else {
    if (data["declined"] === "1") {
      yourAccept.classList.add("redElements");
      yourAccept.dataset.cash = "Declined";
      yourAccept.textContent = "Declined";
    } else {
      yourAccept.classList.add("greenElements");
      yourAccept.dataset.cash = "Accepted";
      yourAccept.textContent = "Accepted";
    }
  }

  var theirAccept = document.createElement("div");
  theirAccept.classList.add("ifiLabel");
  theirAccept.classList.add("cashhack");
  theirAccept.style.right = "4px";
  if (data[theirPlayer + "_accepted"] === "0") {
    theirAccept.classList.add("redElements");
    theirAccept.dataset.cash = "Not Accepted";
    theirAccept.textContent = "Not Accepted";
  } else {
    if (data["declined"] === "1") {
      theirAccept.classList.add("redElements");
      theirAccept.dataset.cash = "Declined";
      theirAccept.textContent = "Declined";
    } else {
      theirAccept.classList.add("greenElements");
      theirAccept.dataset.cash = "Accepted";
      theirAccept.textContent = "Accepted";
    }
  }

  var yourMessage = document.createElement("textarea");
  yourMessage.classList.add("messages");
  yourMessage.style.left = 0;
  yourMessage.value = data[yourPlayer + "_message"];

  yourMessage.dataset.target = data[theirPlayer + "_id"];
  yourMessage.dataset.credits =
    parseInt(data[yourPlayer + "_credits"]) +
    parseInt(data[theirPlayer + "_credits"]);
  yourMessage.dataset.update = data["update_id"];
  yourMessage.dataset.trade = data["trade_id"];
  yourMessage.dataset.action = "changeMessage";

  yourMessage.maxLength = "120";

  if (
    data["trade_id"] !== "0" &&
    data[theirPlayer + "_id"] !== "3" &&
    data[theirPlayer + "_id"] !== "42"
  ) {
    yourMessage.onchange = i4iAction;

    yourMessage.oninput = function (dE) {
      dE.target.value = dE.target.value.replace(
        /[^A-Z a-z 0-9 _ ? ! . , \`\-   ]/g,
        ""
      );
    };
    yourMessage.onkeydown = function (dE) {
      if (dE.key === "'" || dE.key === '"') {
        dE.preventDefault();
        if (dE.target.value.length < 120) {
          dE.target.value += "`";
        }
      }
    };
  } else {
    yourMessage.disabled = true;
  }

  if (
    (data["player1_accepted"] === "1" && data["player2_accepted"] === "1") ||
    data["declined"] === "1"
  ) {
    yourMessage.disabled = true;
  }

  var theirMessage = document.createElement("div");
  theirMessage.classList.add("messages");
  theirMessage.style.right = 0;
  theirMessage.textContent = data[theirPlayer + "_message"];

  var yourTradeZone = document.createElement("div");
  yourTradeZone.classList.add("tradezoneLabels");
  yourTradeZone.style.left = "0px";
  yourTradeZone.style.color = "#12FF00";
  yourTradeZone.textContent =
    tradezoneNamerShort(userVars["DFSTATS_df_tradezone"]) + " Zone";

  var theirTradeZone = document.createElement("div");
  theirTradeZone.classList.add("tradezoneLabels");
  theirTradeZone.style.left = "356px";
  if (data[theirPlayer + "_id"] !== "3" && data[theirPlayer + "_id"] !== "42") {
    if (data["TARGET_df_tradezone"] === userVars["DFSTATS_df_tradezone"]) {
      theirTradeZone.style.color = "#12FF00";
    } else {
      theirTradeZone.style.color = "#FF0000";
    }
    theirTradeZone.textContent =
      tradezoneNamerShort(data["TARGET_df_tradezone"]) + " Zone";
  } else {
    theirTradeZone.style.color = "#12FF00";
    theirTradeZone.textContent = "Everywhere";
  }

  if (data["player1_accepted"] === "0" || data["player2_accepted"] === "0") {
    var acceptHolder = document.createElement("div");
    acceptHolder.classList.add("opElem");
    acceptHolder.style.top = "30px";
    acceptHolder.style.left = "280px";
    acceptHolder.style.right = "280px";

    var acceptButton = document.createElement("button");
    acceptButton.style.fontSize = "14pt";
    if (data[yourPlayer + "_accepted"] !== "1") {
      acceptButton.textContent = "accept";
    } else {
      acceptButton.textContent = "unaccept";
    }
    acceptButton.dataset.action = "acceptTrade";
    acceptButton.dataset.target = data[theirPlayer + "_id"];
    acceptButton.dataset.update = data["update_id"];
    acceptButton.dataset.cash =
      parseInt(data[yourPlayer + "_cash"]) +
      parseInt(data[theirPlayer + "_cash"]);
    acceptButton.dataset.credits =
      parseInt(data[yourPlayer + "_credits"]) +
      parseInt(data[theirPlayer + "_credits"]);
    acceptButton.dataset.trade = data["trade_id"];

    if (
      data["TARGET_df_tradezone"] !== userVars["DFSTATS_df_tradezone"] &&
      data[theirPlayer + "_id"] !== "3" &&
      data[theirPlayer + "_id"] !== "42"
    ) {
      acceptButton.classList.add("disabledElem");
      acceptButton.onmouseenter = function (e) {
        displayPlacementMessage(
          "You are in a different trade zone than " +
            userVars["member_to_name"],
          e.currentTarget.getBoundingClientRect().left - 75,
          e.currentTarget.getBoundingClientRect().bottom + 8,
          "ERROR"
        );
      };
      acceptButton.onmouseout = function (e) {
        cleanPlacementMessage();
      };
    } else if (parseInt(tradeListSize) > userVars["DFSTATS_df_invslots"]) {
      acceptButton.classList.add("disabledElem");
      acceptButton.onmouseenter = function (e) {
        displayPlacementMessage(
          "Your collection box and selling list are full, you cannot accept.",
          e.currentTarget.getBoundingClientRect().left - 75,
          e.currentTarget.getBoundingClientRect().bottom + 8,
          "ERROR"
        );
      };
      acceptButton.onmouseout = function (e) {
        cleanPlacementMessage();
      };
    } else {
      acceptButton.onclick = i4iAction;
    }

    acceptHolder.appendChild(acceptButton);

    var declineHolder = document.createElement("div");
    declineHolder.classList.add("opElem");
    declineHolder.style.top = "50px";
    declineHolder.style.left = "280px";
    declineHolder.style.right = "280px";

    var declineButton = document.createElement("button");
    declineButton.style.fontSize = "14pt";
    declineButton.textContent = "decline";

    declineButton.dataset.action = "declineTrade";
    declineButton.dataset.target = data[theirPlayer + "_id"];
    declineButton.dataset.update = data["update_id"];
    declineButton.dataset.cash =
      parseInt(data[yourPlayer + "_cash"]) +
      parseInt(data[theirPlayer + "_cash"]);
    declineButton.dataset.credits =
      parseInt(data[yourPlayer + "_credits"]) +
      parseInt(data[theirPlayer + "_credits"]);
    declineButton.dataset.trade = data["trade_id"];

    if (data["trade_id"] === "0") {
      declineButton.classList.add("disabledElem");
      declineButton.onmouseenter = function (e) {
        displayPlacementMessage(
          "You must start a trade to decline it",
          e.currentTarget.getBoundingClientRect().left - 70,
          e.currentTarget.getBoundingClientRect().bottom + 8,
          "ERROR"
        );
      };
      declineButton.onmouseout = function (e) {
        cleanPlacementMessage();
      };
    } else if (
      data[theirPlayer + "_id"] === "3" ||
      data[theirPlayer + "_id"] === "42"
    ) {
      declineButton.classList.add("disabledElem");
      declineButton.onmouseenter = function (e) {
        displayPlacementMessage(
          "You cannot decline trades from Help Bot",
          e.currentTarget.getBoundingClientRect().left - 70,
          e.currentTarget.getBoundingClientRect().bottom + 8,
          "ERROR"
        );
      };
      declineButton.onmouseout = function (e) {
        cleanPlacementMessage();
      };
    } else {
      declineButton.onclick = i4iAction;
    }

    declineHolder.appendChild(declineButton);

    marketHolder.appendChild(acceptHolder);
    marketHolder.appendChild(declineHolder);

    if (data["trade_id"] !== "0") {
      startQueryUpdate(data["trade_id"], data[theirPlayer + "_id"]);
    }
  }

  marketHolder.appendChild(yourLabel);
  marketHolder.appendChild(theirLabel);

  marketHolder.appendChild(yourCash);
  marketHolder.appendChild(yourCashInput);
  marketHolder.appendChild(theirCash);

  marketHolder.appendChild(yourCredits);
  marketHolder.appendChild(yourCreditsInput);
  marketHolder.appendChild(theirCredits);

  marketHolder.appendChild(yourAccept);
  marketHolder.appendChild(theirAccept);

  marketHolder.appendChild(yourMessage);
  marketHolder.appendChild(theirMessage);

  marketHolder.appendChild(yourHolder);
  marketHolder.appendChild(theirHolder);

  marketHolder.appendChild(yourTradeZone);
  marketHolder.appendChild(theirTradeZone);

  if (!inPrivateTrading) {
    var backButton = document.createElement("button");
    backButton.classList.add("opElem");
    backButton.textContent = "< TRADE PANEL";
    backButton.style.left = "12px";
    backButton.style.top = "8px";
    backButton.onclick = function () {
      stopQueryUpdate();
      loadMarket();
    };
    marketHolder.appendChild(backButton);
  } else {
    var privateTradeButton = document.createElement("button");
    privateTradeButton.classList.add("opElem");
    privateTradeButton.textContent = "Private Trade";
    privateTradeButton.onclick = function () {
      initiatePrivateTrade();
      stopQueryUpdate();
    };
    privateTradeButton.style.right = "60px";
    privateTradeButton.style.top = "10px";

    marketHolder.appendChild(privateTradeButton);
  }

  if (data["player1_accepted"] === "1" && data["player2_accepted"] === "1") {
    if (data["declined"] === "1") {
      declinedTradeAction();
    } else {
      successfulTradeAction();
    }
  } else {
    promptEnd();
  }
}

function startQueryUpdate(tradeID, targetID) {
  tradeTimeout = setTimeout(function () {
    if (
      document.activeElement.dataset.action === "changeMessage" ||
      document.activeElement.dataset.action === "changeCurrency"
    ) {
      startQueryUpdate(tradeID, targetID);
    } else {
      if (requestTrack === 5) {
        tradeTimer = tradeTimer * 2;
        requestTrack = 1;
      } else {
        requestTrack++;
      }
      if (inTradeWindow) {
        loadPrivateItemForItem(tradeID, targetID);
      }
    }
  }, tradeTimer);
}

function stopQueryUpdate() {
  clearTimeout(tradeTimeout);
  tradeTimer = 30000;
  requestTrack = 0;
}

function i4iAction(e) {
  stopQueryUpdate();
  var question = false;
  inTradeWindow = false;
  var action;
  var extraData = {};
  switch (e.target.dataset.action) {
    case "acceptTrade":
      extraData["target"] = e.target.dataset.target;
      extraData["cash"] = e.target.dataset.cash;
      extraData["credits"] = e.target.dataset.credits;
      extraData["update"] = e.target.dataset.update;
      extraData["trade"] = e.target.dataset.trade;
      extraData["action"] = "accept";

      df_prompt.innerHTML = "Are you sure you want to ";
      if (e.target.textContent === "accept") {
        df_prompt.innerHTML += "<span style='color: #00ff00;'>accept</span>";
      } else {
        extraData["action"] = "un" + extraData["action"];
        df_prompt.innerHTML += "<span style='color: #ff0000;'>unaccept</span>";
      }
      df_prompt.innerHTML += " this trade?";
      action = doTradeAction;
      question = true;
      break;
    case "declineTrade":
      extraData["target"] = e.target.dataset.target;
      extraData["cash"] = e.target.dataset.cash;
      extraData["credits"] = e.target.dataset.credits;
      extraData["update"] = e.target.dataset.update;
      extraData["trade"] = e.target.dataset.trade;
      extraData["action"] = "decline";
      df_prompt.innerHTML =
        "<span style='color: red;'>Are you sure you want to decline this trade?</span>";
      df_prompt.innerHTML += "<br />Items will appear in your Collection Box.";
      action = declineAction;
      question = true;
      break;
    case "removeItem":
      var itemData = e.target.parentNode.dataset.type.split("_");

      extraData["itemnum"] = findFirstEmptyGenericSlot("inv");
      extraData["target"] = e.target.dataset.target;
      extraData["credits"] = e.target.dataset.credits;
      extraData["update"] = e.target.dataset.update;
      extraData["trade"] = e.target.dataset.trade;
      extraData["type"] = e.target.parentNode.dataset.type;
      extraData["cash"] = e.target.parentNode.dataset.quantity;
      extraData["action"] = "removeitem";
      df_prompt.innerHTML =
        "Remove <span style='color: #ff0000;'>" +
        globalData[itemData[0]]["name"] +
        "</span> from trade?";
      action = doTradeAction;
      question = true;
      break;
    case "changeCurrency":
      extraData["cash"] = parseInt(e.target.value);
      if (e.target.value !== "") {
        if (extraData["cash"] <= e.currentTarget.max) {
          extraData["target"] = e.target.dataset.target;

          extraData["credits"] = e.target.dataset.credits;
          extraData["update"] = e.target.dataset.update;
          extraData["trade"] = e.target.dataset.trade;
          extraData["action"] = e.target.dataset.currency;
          df_prompt.innerHTML =
            "Change <span style='color: #ff0000;'>" +
            e.target.dataset.currency +
            "</span> to <span style='color: #ff0000;'>" +
            nf.format(parseInt(extraData["cash"])) +
            "</span>?";
          action = doTradeAction;
          question = true;
        } else {
          df_prompt.innerHTML =
            "You don't have enough <span style='color: #ff0000;'>" +
            e.target.dataset.currency +
            "</span> to offer <span style='color: #ff0000;'>" +
            nf.format(parseInt(e.target.value)) +
            "</span>.";
        }
      } else {
        df_prompt.innerHTML = "Please enter a value that is a number.</span>.";
      }
      break;
    case "changeMessage":
      extraData["target"] = e.target.dataset.target;
      extraData["cash"] = e.target.dataset.cash;
      extraData["credits"] = e.target.dataset.credits;
      extraData["update"] = e.target.dataset.update;
      extraData["trade"] = e.target.dataset.trade;
      extraData["action"] = "message";
      extraData["type"] = e.target.value;
      df_prompt.innerHTML = "Are you sure you want to change the message?";
      action = doTradeAction;
      question = true;
      break;
    case "collectItem":
      var itemData = e.target.parentNode.dataset.type.split("_");

      extraData["itemnum"] = findFirstEmptyGenericSlot("inv");
      extraData["target"] = e.target.dataset.target;
      extraData["credits"] = e.target.dataset.credits;
      extraData["update"] = 0;
      extraData["trade"] = e.target.parentNode.dataset.trade;
      extraData["type"] = e.target.parentNode.dataset.type;
      extraData["cash"] = e.target.parentNode.dataset.quantity;
      extraData["action"] = "collect";
      df_prompt.innerHTML =
        "Are you sure you want to collect <span style='color: #ff0000;'>" +
        itemNamer(
          e.target.parentNode.dataset.type,
          e.target.parentNode.dataset.quantity
        ) +
        "</span>?";
      action = doCollectAction;
      question = true;
      break;
    case "collectAllItems":
      extraData["action"] = "collectall";
      df_prompt.innerHTML = "Are you sure you want to collect ALL items?";
      action = doCollectAllAction;
      question = true;
      break;
    default:
      console.log(e.target.dataset.action);
      return;
      break;
  }
  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.top = "72px";
  noButton.addEventListener("click", function () {
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    pageLock = false;
  });
  if (question) {
    noButton.textContent = "No";
    noButton.style.right = "86px";
    var yesButton = document.createElement("button");
    yesButton.textContent = "Yes";
    yesButton.style.position = "absolute";
    yesButton.style.left = "86px";
    yesButton.style.top = "72px";
    yesButton.addEventListener("click", function () {
      yesButton.disabled = true;
      action(extraData);
    });

    df_prompt.appendChild(yesButton);
    df_prompt.onkeydown = function (e) {
      if (e.keyCode === 13) {
        yesButton.click();
      }
    };
  } else {
    noButton.textContent = "ok";
    noButton.style.left = "125px";
    df_prompt.onkeydown = function (e) {
      if (e.keyCode === 13) {
        noButton.click();
      }
    };
  }
  df_prompt.appendChild(noButton);
  df_prompt.parentNode.style.display = "block";
  df_prompt.focus();
}

function declineAction(tradeData) {
  df_prompt.innerHTML =
    "<span style='color: #ff0000;'>Are you absolutely sure?</span>";
  df_prompt.innerHTML += "<br />Cash will be deposited into your bank.";

  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.top = "72px";
  noButton.addEventListener("click", function () {
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    pageLock = false;
  });
  noButton.textContent = "No";
  noButton.style.right = "86px";
  var yesButton = document.createElement("button");
  yesButton.textContent = "Yes";
  yesButton.style.position = "absolute";
  yesButton.style.left = "86px";
  yesButton.style.top = "72px";
  yesButton.addEventListener("click", function () {
    doTradeAction(tradeData);
  });
  df_prompt.appendChild(yesButton);
  df_prompt.appendChild(noButton);
  df_prompt.onkeydown = function (e) {
    if (e.keyCode === 13) {
      df_prompt.onkeydown = null;
      yesButton.click();
    }
  };
  df_prompt.focus();
}

function doTradeAction(tradeData) {
  var dataArray = {};

  dataArray["pagetime"] = userVars["pagetime"];
  dataArray["templateID"] = userVars["template_ID"];
  dataArray["sc"] = userVars["sc"];
  dataArray["creditsnum"] = tradeData["credits"];
  dataArray["buynum"] = tradeData["update"];
  dataArray["price"] = tradeData["cash"];
  dataArray["memberto"] = tradeData["target"];
  dataArray["action"] = tradeData["action"];
  dataArray["userID"] = userVars["userID"];
  dataArray["password"] = userVars["password"];
  dataArray["trade"] = tradeData["trade"];
  dataArray["itemnum"] = tradeData["itemnum"];
  dataArray["expected_itemtype"] = tradeData["type"];

  promptLoading("Loading, please wait...");
  var callURL = "private_trade";
  webCall(
    callURL,
    dataArray,
    function (data) {
      updateIntoArr(flshToArr(data), userVars);
      if (
        typeof userVars["status"] !== "undefined" &&
        userVars["status"] !== ""
      ) {
        switch (userVars["status"]) {
          case "0":
            promptLoading("An unknown error occured...");
            location.reload(true);
            break;
          case "1":
            if (inPrivateTrading) {
              promptLoading("An error has occured. Reloading...");
              location.reload(true);
            } else {
              promptLoading(
                "An error has occured. Going back to the trade panel..."
              );
              loadMarket();
            }
            break;
          case "2":
            if (inPrivateTrading) {
              promptLoading("Player not found. Going to outpost screen...");
              document.location.href = "index.php";
            } else {
              promptLoading(
                "Player not found. Going back to the trade panel..."
              );
              loadMarket();
            }
            break;
          case "3":
            promptLoading("Slot is occupied. Reloading inventory data...");
            reloadInventoryData();
            break;
          case "4":
            promptLoading(
              "Something about this trade has changed...<br />Please wait while we reload."
            );
            tradeTimer = 2500;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
          case "5":
            promptLoading(
              "Your message was too long, please make it shorter..."
            );
            setTimeout(reloadInventoryData, 2500);
            break;
          case "6":
            promptLoading(
              "You have too many items in trade, please remove an item or collect items from your collection box..."
            );
            tradeTimer = 2500;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
          case "7":
            promptLoading("Trade zones do not match...");
            tradeTimer = 2500;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
          case "8":
            promptLoading(
              "Too many items in your collection box or item-for-item trades..."
            );
            tradeTimer = 5000;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
          case "9":
            promptLoading("This user is not receiving messages from you...");
            tradeTimer = 5000;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
          case "10":
            promptLoading("You cannot decline Help Bot.");
            tradeTimer = 5000;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
          case "11":
            promptLoading("Attempted to trade non-transferable item.");
            tradeTimer = 5000;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
          case "12":
            promptLoading("Ammo stack is too big for trade.");
            tradeTimer = 5000;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
          case "13":
            promptLoading(
              "Too many items in their collection box or item-for-item trades..."
            );
            tradeTimer = 5000;
            startQueryUpdate(tradeData["trade"], tradeData["target"]);
            tradeTimer = 30000;
            break;
        }
        userVars["status"] = "";
        return;
      }
      if (userVars["tradelist_totalsales"]) {
        tradeListSize = userVars["tradelist_totalsales"];
      }
      populateInventory();
      updateAllFieldsBase();
      populatePrivateItemForItem(data);
    },
    true
  );
}

function successfulTradeAction() {
  df_prompt.style.height = "125px";
  if (inPrivateTrading) {
    df_prompt.innerHTML =
      "This trade is complete, please go to the market to collect your items. Cash was deposited in your bank and credits have been rewarded to you.";
  } else {
    df_prompt.innerHTML =
      "Trade finished, please collect items in the <button>item collection box</button> Your bank has received the cash and Credits have been transfered.";
    df_prompt
      .querySelector("button")
      .addEventListener("click", displayCollectionBox);
  }

  var completeTradeButtonHolder = document.createElement("div");
  completeTradeButtonHolder.style.position = "absolute";
  completeTradeButtonHolder.style.bottom = "12px";
  completeTradeButtonHolder.style.width = "100%";
  completeTradeButtonHolder.style.textAlign = "center";

  var noButton = document.createElement("button");

  noButton.addEventListener("click", function () {
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    df_prompt.style.height = "";
    pageLock = false;
  });
  noButton.textContent = "[close message]";

  var collectionButton = document.createElement("button");
  collectionButton.addEventListener("click", displayCollectionBox);
  collectionButton.textContent = "[collection box]";
  collectionButton.style.marginLeft = "10px";

  df_prompt.onkeydown = function (e) {
    if (e.keyCode === 13) {
      noButton.click();
    }
  };

  completeTradeButtonHolder.appendChild(noButton);
  completeTradeButtonHolder.appendChild(collectionButton);
  df_prompt.appendChild(completeTradeButtonHolder);
  df_prompt.parentNode.style.display = "block";
  df_prompt.focus();
}

function declinedTradeAction() {
  df_prompt.style.height = "125px";
  if (inPrivateTrading) {
    df_prompt.innerHTML =
      "This trade was declined, please go to the market to collect your items. Cash was returned to your bank and credits have been returned to you.";
  } else {
    df_prompt.innerHTML =
      "Trade declined, please collect items in the <button>item collection box</button> Your bank has received your cash and Credits have been returned.";
    df_prompt
      .querySelector("button")
      .addEventListener("click", displayCollectionBox);
  }

  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.bottom = "12px";
  noButton.addEventListener("click", function () {
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    df_prompt.style.height = "";
    pageLock = false;
  });
  noButton.textContent = "ok";
  noButton.style.left = "125px";
  df_prompt.onkeydown = function (e) {
    if (e.keyCode === 13) {
      noButton.click();
    }
  };

  df_prompt.appendChild(noButton);
  df_prompt.parentNode.style.display = "block";
  df_prompt.focus();
}

function doCollectAction(tradeData) {
  var dataArray = {};

  dataArray["pagetime"] = userVars["pagetime"];
  dataArray["templateID"] = userVars["template_ID"];
  dataArray["sc"] = userVars["sc"];
  dataArray["creditsnum"] = tradeData["credits"];
  dataArray["buynum"] = tradeData["update"];
  dataArray["price"] = tradeData["cash"];
  dataArray["memberto"] = tradeData["target"];
  dataArray["action"] = tradeData["action"];
  dataArray["userID"] = userVars["userID"];
  dataArray["password"] = userVars["password"];
  dataArray["trade"] = tradeData["trade"];
  dataArray["itemnum"] = tradeData["itemnum"];
  dataArray["expected_itemtype"] = tradeData["type"];

  df_prompt.innerHTML =
    "<div style='text-align: center'>Loading, please wait...</div>";
  var callURL = "private_trade";
  webCall(
    callURL,
    dataArray,
    function (data) {
      updateIntoArr(flshToArr(data), userVars);
      if (userVars["status"]) {
        switch (userVars["status"]) {
          case "0":
            promptLoading("An unknown error occured...");
            location.reload(true);
            break;
          case "1":
            if (inPrivateTrading) {
              promptLoading("An error has occured. Reloading...");
              location.reload(true);
            } else {
              promptLoading(
                "An error has occured. Going back to the trade panel..."
              );
              loadMarket();
            }
            break;
          case "2":
            if (inPrivateTrading) {
              promptLoading("Player not found. Going to outpost screen...");
              document.location.href = "index.php";
            } else {
              promptLoading(
                "Player not found. Going back to the trade panel..."
              );
              loadMarket();
            }
            break;
          case "3":
            promptLoading("Slot is occupied. Reloading inventory data...");
            reloadInventoryData();
            break;
        }
        userVars["status"] = "";
        return;
      }
      if (userVars["tradelist_totalsales"]) {
        tradeListSize = userVars["tradelist_totalsales"];
      }
      populateInventory();
      updateAllFieldsBase();
      displayCollectionBox();
    },
    true
  );
}

function doCollectAllAction(tradeData) {
  var dataArray = {};

  dataArray["pagetime"] = userVars["pagetime"];
  dataArray["templateID"] = userVars["template_ID"];
  dataArray["sc"] = userVars["sc"];
  dataArray["action"] = tradeData["action"];
  dataArray["userID"] = userVars["userID"];
  dataArray["password"] = userVars["password"];

  df_prompt.innerHTML =
    "<div style='text-align: center'>Loading, please wait...</div>";
  var callURL = "private_trade";
  webCall(
    callURL,
    dataArray,
    function (data) {
      updateIntoArr(flshToArr(data), userVars);
      if (userVars["status"]) {
        switch (userVars["status"]) {
          case "0":
            promptLoading("An unknown error occured...");
            location.reload(true);
            break;
          case "1":
            if (inPrivateTrading) {
              promptLoading("An error has occured. Reloading...");
              location.reload(true);
            } else {
              promptLoading(
                "An error has occured. Going back to the trade panel..."
              );
              loadMarket();
            }
            break;
          case "2":
            if (inPrivateTrading) {
              promptLoading("Player not found. Going to outpost screen...");
              document.location.href = "index.php";
            } else {
              promptLoading(
                "Player not found. Going back to the trade panel..."
              );
              loadMarket();
            }
            break;
          case "3":
            promptLoading("Slot is occupied. Reloading inventory data...");
            reloadInventoryData();
            break;
        }
        userVars["status"] = "";
        return;
      }
      if (userVars["tradelist_totalsales"]) {
        tradeListSize = userVars["tradelist_totalsales"];
      }
      reloadInventoryData();
      displayCollectionBox();
    },
    true
  );
}
