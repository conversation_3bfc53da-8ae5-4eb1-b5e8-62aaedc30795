var oaSound = new Audio();
oaSound.loop = true;
//oaSound.src = 'hotrods_v'+hrV+'/HTML5/sounds/siren.mp3';
var outpostData;
var outpostAttack = false;
var outpostLocation = "";
var arenaCount = 0;

function initOutpost() {
  df_prompt = document.getElementById("gamecontent");
  var outpostType = 0;
  if (userVars["minioutpost"] == 0) {
    outpostType = 1;
  } else {
    // 1-20 IC
    // 21+ Outposts
    switch (parseInt(userVars["df_tradezone"])) {
      case 1:
        outpostLocation = "North Western";
        break;
      case 2:
        outpostLocation = "Northern";
        break;
      case 3:
        outpostLocation = "North Eastern";
        break;
      case 4:
        outpostLocation = "Western";
        break;
      case 5:
        outpostLocation = "Central";
        break;
      case 6:
        outpostLocation = "Eastern";
        break;
      case 7:
        outpostLocation = "South Western";
        break;
      case 8:
        outpostLocation = "Southern";
        break;
      case 9:
        outpostLocation = "South Eastern";
        break;
      case 10:
        outpostLocation = "Wasteland";
        break;
      case 21:
        outpostLocation = "Outpost";
        break;
      case 22: // Valcrest
        outpostLocation = "Valcrest";
        break;
    }

    if (userVars["df_tradezone"] === "21") {
      switch (userVars["minioutpostname"]) {
        case "Nastya`s Holdout":
          outpostType = 1;
          outpostLocation = "";
          break;
        case "Dogg`s Stockade":
          outpostType = 2;
          outpostLocation = "";
          break;
        case "Precinct 13":
          outpostType = 3;
          outpostLocation = "";
          break;
        case "Fort Pastor":
          outpostType = 4;
          outpostLocation = "";
          break;
        case "Secronom Bunker":
          outpostType = 5;
          outpostLocation = "";
          break;
      }
    }
    if (userVars["df_tradezone"] === "22") {
      outpostType = 6;
      outpostLocation = "";
    }
  }

  if (userVars["defenseon"] === "1") {
    outpostAttack = true;
  }
  arenaCount = parseInt(userVars["arenacount"]);
  let xhr = new XMLHttpRequest();
  xhr.open("GET", `hotrods/hotrods_v${hrV}/HTML5/json/outpost/base.json`);
  xhr.onload = function () {
    outpostData = JSON.parse(xhr.responseText);
    let xhr2 = new XMLHttpRequest();
    xhr2.onload = function () {
      outpostData = array_replace_recursive(
        outpostData,
        JSON.parse(xhr2.responseText)
      );
      completeOutpostInit();
    };
    if (outpostType === 0) {
      xhr2.open(
        "GET",
        `hotrods/hotrods_v${hrV}/HTML5/json/outpost/personal.json`
      );
    } else {
      switch (outpostType) {
        case 1:
          xhr2.open(
            "GET",
            `hotrods/hotrods_v${hrV}/HTML5/json/outpost/nastya.json`
          );
          break;
        case 2:
          xhr2.open(
            "GET",
            `hotrods/hotrods_v${hrV}/HTML5/json/outpost/doggs.json`
          );
          break;
        case 3:
          xhr2.open(
            "GET",
            `hotrods/hotrods_v${hrV}/HTML5/json/outpost/precinct.json`
          );
          break;
        case 4:
          xhr2.open(
            "GET",
            `hotrods/hotrods_v${hrV}/HTML5/json/outpost/fort.json`
          );
          break;
        case 5:
          xhr2.open(
            "GET",
            `hotrods/hotrods_v${hrV}/HTML5/json/outpost/bunker.json`
          );
          break;
        case 6:
          xhr2.open(
            "GET",
            `hotrods/hotrods_v${hrV}/HTML5/json/outpost/valcrest.json`
          );
          break;
        default:
          xhr2.open(
            "GET",
            `hotrods/hotrods_v${hrV}/HTML5/json/outpost/nastya.json`
          );
          break;
      }
    }
    xhr2.send();
  };
  xhr.send();
}

function completeOutpostInit() {
  var outpostElem = document.getElementById("outpost");
  outpostElem.style.backgroundImage =
    "url(hotrods/hotrods_v" +
    hrV +
    "/HTML5/images/" +
    outpostData["background"] +
    ")";
  var pageLogo = document.createElement("div");
  //pageLogo.classList.add("opElem");
  pageLogo.id = "pageLogo";
  pageLogo.setAttribute(
    "style",
    "background-image: url(hotrods/hotrods_v" +
      hrV +
      "/HTML5/images/" +
      outpostData["logo"]["image"] +
      "); background-position: " +
      outpostData["logo"]["location"] +
      ";"
  );
  var innerLogo = document.createElement("div");
  innerLogo.style.textAlign = outpostData["logo"]["location"];
  innerLogo.style.position = "relative";
  innerLogo.style.top = "65px";
  innerLogo.style.color = "#26E626";
  if (outpostLocation !== "") {
    var outpostNameData = document.createElement("span");
    outpostNameData.textContent = outpostLocation + " Zone";
    innerLogo.appendChild(outpostNameData);
  }
  if (outpostAttack) {
    var oaIndicator = document.createElement("span");
    oaIndicator.classList.add("outpostAttack");
    oaIndicator.textContent = "OUTPOST IS UNDER ATTACK!";
    oaIndicator.addEventListener("click", shutUpOa);
    innerLogo.appendChild(oaIndicator);
    if (
      checkLSBool("general", "playSound") &&
      checkPropertyValid("general", "volume")
    ) {
      if (oaSound.canPlayType("audio/mp3") !== "") {
        oaSound.src = "hotrods/hotrods_v" + hrV + "/HTML5/sounds/siren.mp3";
      } else if (oaSound.canPlayType("audio/ogg") !== "") {
        oaSound.src = "hotrods/hotrods_v" + hrV + "/HTML5/sounds/siren.ogg";
      }
      oaSound.volume = parseInt(userSettings["general"]["volume"]) / 100;
      oaSound.play();
    }
  }
  pageLogo.appendChild(innerLogo);

  let isIronman = false;
  let isHardcore = false;
  if (userVars["df_profession"].indexOf("Ironman") !== -1) {
    isIronman = true;
  }
  if (userVars["df_profession"].indexOf("Hardcore") !== -1) {
    isHardcore = true;
  }

  outpostElem.appendChild(pageLogo);
  for (var i in outpostData["links"]) {
    if (outpostData["links"][i] && outpostData["links"][i]["location"]) {
      if (
        (outpostData["links"][i]["noIrons"] && isIronman) ||
        (outpostData["links"][i]["noHardcore"] && isHardcore)
      ) {
        continue;
      }
      if (
        typeof outpostData["links"][i]["userVarCheck"] !== "undefined" &&
        userVars[outpostData["links"][i]["userVarCheck"]] !== "1"
      ) {
        continue;
      }
      var lData = outpostData["links"][i];
      var appendElem = document.createElement("div");
      appendElem.classList.add("opElem");
      appendElem.style.top = lData["location"]["top"] + "px";
      appendElem.style.left = lData["location"]["left"] + "px";
      var btn = document.createElement("button");
      btn.dataset.page = lData["page"];
      if (lData["mod"]) {
        btn.dataset.mod = lData["mod"];
      } else {
        btn.dataset.mod = 0;
      }
      btn.dataset.sound = 1;
      btn.addEventListener("mousedown", nChangePage);
      if (i === "innercity") {
        if (userVars["df_cash"] && parseInt(userVars["df_cash"]) > 0) {
          btn.addEventListener("mouseenter", function (e) {
            if (e.target.dataset.page === "21") {
              var floatingText = document.getElementById("textAddon");
              floatingText.textContent = "Be careful, you have money on you";
              floatingText.style.width = "165px";
              floatingText.style.display = "block";
              floatingText.style.top =
                outpostData["links"]["innercity"]["location"]["top"] +
                40 +
                "px";
              floatingText.style.left =
                outpostData["links"]["innercity"]["location"]["left"] -
                60 +
                "px";
              floatingText.style.color = "red";
            }
          });
          btn.addEventListener("mouseout", function (e) {
            if (e.relatedTarget.dataset.page !== "21") {
              var floatingText = document.getElementById("textAddon");
              floatingText.style.display = "none";
            }
          });
        }
      }
      if (i === "arcade") {
        btn.removeEventListener("mousedown", nChangePage);
        btn.addEventListener("click", displayArcadeGames);
      }
      btn.innerHTML = lData["content"];
      if (btn.innerHTML === "Arena") {
        btn.innerHTML += " (" + arenaCount + ")";
      }
      appendElem.appendChild(btn);
      outpostElem.appendChild(appendElem);
    }
  }
  var setBtn = document.createElement("div");
  setBtn.id = "settingsButton";
  setBtn.addEventListener("click", loadSettings);
  outpostElem.appendChild(setBtn);
  var frm = document.createElement("iframe");
  frm.id = "settingsBox";
  outpostElem.parentNode.appendChild(frm);
  outpostElem.parentNode.style.position = "relative";
}

function displayArcadeGames() {
  var title = document.createElement("h3");
  title.textContent = "Arcade Games";
  title.style.margin = "2px";
  title.style.padding = 0;
  title.style.textAlign = "center";

  var arcadeBox = document.createElement("div");
  arcadeBox.style.textAlign = "center";
  arcadeBox.style.marginTop = "20px";

  var gameLink = document.createElement("a");
  gameLink.textContent = "Night One";
  gameLink.href = "https://deadfrontier.com/dfnightone/";
  gameLink.style.marginRight = "2px";
  arcadeBox.appendChild(gameLink);
  arcadeBox.appendChild(document.createElement("br"));

  gameLink = document.createElement("a");
  gameLink.textContent = "Night Two";
  gameLink.href = "https://deadfrontier.com/dfnighttwo/";
  gameLink.style.marginRight = "2px";
  gameLink.style.marginLeft = "2px";
  arcadeBox.appendChild(gameLink);
  arcadeBox.appendChild(document.createElement("br"));

  gameLink = document.createElement("a");
  gameLink.textContent = "Night Three";
  gameLink.href = "https://deadfrontier.com/dfnightthree/";
  gameLink.style.marginLeft = "2px";
  arcadeBox.appendChild(gameLink);
  arcadeBox.appendChild(document.createElement("br"));

  gameLink = document.createElement("a");
  gameLink.textContent = "Night Four";
  gameLink.href = "https://files.deadfrontier.com/deadfrontier/night4/";
  gameLink.style.marginLeft = "2px";
  arcadeBox.appendChild(gameLink);
  arcadeBox.appendChild(document.createElement("br"));

  gameLink = document.createElement("a");
  gameLink.textContent = "Corner Shop";
  gameLink.href = "https://files.deadfrontier.com/deadfrontier/cornershop/";
  gameLink.style.marginLeft = "2px";
  arcadeBox.appendChild(gameLink);

  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.bottom = "5px";
  noButton.style.left = "115px";
  noButton.addEventListener("click", function () {
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    df_prompt.style.height = "";
  });
  noButton.textContent = "Close";

  df_prompt.innerHTML = "";
  df_prompt.style.height = "150px";
  df_prompt.appendChild(title);
  df_prompt.appendChild(arcadeBox);
  df_prompt.appendChild(noButton);
  df_prompt.parentNode.style.display = "block";
}

function shutUpOa() {
  if (oaSound.paused) {
    oaSound.play();
  } else {
    oaSound.pause();
  }
}
