import { Log } from "@/lib/common/log";
import { uiAddMoveStorageButtons } from "@/lib/config/move-storage";
import { removeOutpostButtonDelay } from "@/lib/config/outpost-delay";
import { augmentInfoCardWithPriceInfo } from "@/lib/config/price-info-card";
import { Storage } from "@/lib/common/inventory-storage";
import { addQuickActionsToSidebar } from "./lib/config/sidebar-quick-actions";

const CONFIGS: Record<string, Array<() => void>> = {
  // all pages except innercity inventory
  "index\\.php": [],
  // outpost
  "index\\.php$": [removeOutpostButtonDelay],
  // yard
  "index\\.php\\?page=24": [
    Storage.loadStorageData,
    uiAddMoveStorageButtons,
    augmentInfoCardWithPriceInfo,
  ],
  // outpost inventory
  "index\\.php\\?page=25": [
    Storage.loadStorageData,
    uiAddMoveStorageButtons,
    augmentInfoCardWithPriceInfo,
  ],
  // innercity inventory
  "DF3D_InventoryPage\\.php\\?page=31": [augmentInfoCardWithPriceInfo],
  // marketplace
  "index\\.php\\?page=35": [
    Storage.loadStorageData,
    uiAddMoveStorageButtons,
    augmentInfoCardWithPriceInfo,
    addQuickActionsToSidebar,
  ],
  // storage
  "index\\.php\\?page=50": [augmentInfoCardWithPriceInfo],
};

unsafeWindow.addEventListener("load", () => {
  let path = unsafeWindow.location.href.split("/").pop()!;
  Log.info("applying config for", path);

  for (const regex in CONFIGS) {
    if (path.match(regex)) {
      Log.info("rule matched", regex);
      const config = CONFIGS[regex];
      config.forEach((f) => {
        Log.info("applying config", f.name);
        f();
      });
    }
  }

  Log.info("config applied");
});
