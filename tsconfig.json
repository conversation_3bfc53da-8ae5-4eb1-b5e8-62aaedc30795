{
  "compilerOptions": {
    "rootDir": "./src",

    "module": "none",
    "target": "esnext",

    // Other Outputs
    "sourceMap": false,
    "declaration": false,
    "declarationMap": false,
    "removeComments": true,

    // Stricter Typechecking Options
    "noUncheckedIndexedAccess": true,

    // Style Options
    "noImplicitReturns": true,
    "noImplicitOverride": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noPropertyAccessFromIndexSignature": true,

    // Recommended Options
    "strict": false,
    "noUncheckedSideEffectImports": true,
    "skipLibCheck": true,

    // Module Resolution
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },

    // allow jsx usage for DOM manipulation
    "jsx": "react",
    "jsxFactory": "h"
  }
}
